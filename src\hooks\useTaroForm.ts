import type { FormProps } from '@tarojs/components';
import { useState } from 'react';
import { type ZodSchema, ZodError } from 'zod';

interface UseTaroFormProps<T> {
  schema: ZodSchema<T>;
}

type FormErrors = Record<string, { message: string }>;

export const useTaroForm = <T extends Record<string, unknown>>({ schema }: UseTaroFormProps<T>) => {
  // 表单错误状态
  const [errors, setErrors] = useState<FormErrors>({});
  // 表单值状态（可选，仅用于追踪和调试）
  const [formValues, setFormValues] = useState<Partial<T>>({});

  // 字段注册函数 - 简单地返回字段名
  const register = (name: keyof T) => {
    return { name: String(name) };
  };

  // 表单提交处理函数
  const handleSubmit = (onSubmit: (data: T) => void) => {
    return (event: FormProps.onSubmitEventDetail) => {
      // 阻止默认表单提交
      // event.preventDefault?.();

      // 从Taro表单事件获取表单数据
      const formData = event?.value || {};
      console.log('表单提交数据:', formData);

      // 更新内部表单值状态
      setFormValues(formData as Partial<T>);

      // 使用Zod验证数据
      try {
        const validatedData = schema.parse(formData) as T;
        // 验证成功，清除所有错误
        setErrors({});
        // 调用提交回调
        onSubmit(validatedData);
      } catch (err) {
        if (err instanceof ZodError) {
          // 处理验证错误
          const newErrors: FormErrors = {};

          for (const error of err.errors) {
            // 获取字段名
            const field = String(error.path[0]);
            const fieldValue = formData[field];

            // 智能错误处理：
            // 1. 对于空字段显示"必填"错误
            // 2. 对于非空字段显示格式/验证错误
            if (error.code === 'invalid_type' && error.message.includes('Required')) {
              // 字段为空时显示必填错误
              if (!fieldValue || fieldValue === '') {
                newErrors[field] = { message: error.message };
              }
            } else {
              // 其他类型的错误总是显示（格式错误、长度错误等）
              newErrors[field] = { message: error.message };
            }
          }

          console.log('表单验证错误:', newErrors);
          setErrors(newErrors);
        } else {
          // 处理其他错误
          console.error('未知错误:', err);
        }
      }
    };
  };

  // 手动设置字段错误
  const setError = (field: keyof T, message: string) => {
    setErrors((prev) => ({
      ...prev,
      [field]: { message },
    }));
  };

  // 清除特定字段错误
  const clearError = (field: keyof T) => {
    setErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[String(field)];
      return newErrors;
    });
  };

  // 清除所有错误
  const clearErrors = () => {
    setErrors({});
  };

  // 重置表单
  const reset = () => {
    setFormValues({});
    setErrors({});
  };

  // 暴露API
  return {
    register,
    handleSubmit,
    errors,
    setError,
    clearError,
    clearErrors,
    reset,
    formValues,
  };
};
