{"name": "jcec_ecard", "version": "1.0.0", "private": true, "description": "JCEC电子名片", "templateInfo": {"name": "default", "typescript": true, "css": "None", "framework": "React"}, "scripts": {"postinstall": "weapp-tw patch", "build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:harmony-hybrid": "taro build --type harmony-hybrid", "dev:weapp": "pnpm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:harmony-hybrid": "npm run build:harmony-hybrid -- --watch"}, "browserslist": ["defaults and fully supports es6-module", "maintained node versions"], "author": "", "dependencies": {"@babel/runtime": "^7.24.4", "@tanstack/react-query": "^5.71.10", "@tarojs/components": "4.0.9", "@tarojs/helper": "4.0.9", "@tarojs/plugin-framework-react": "4.0.9", "@tarojs/plugin-platform-alipay": "4.0.9", "@tarojs/plugin-platform-h5": "4.0.9", "@tarojs/plugin-platform-harmony-hybrid": "4.0.9", "@tarojs/plugin-platform-jd": "4.0.9", "@tarojs/plugin-platform-qq": "4.0.9", "@tarojs/plugin-platform-swan": "4.0.9", "@tarojs/plugin-platform-tt": "4.0.9", "@tarojs/plugin-platform-weapp": "4.0.9", "@tarojs/react": "4.0.9", "@tarojs/runtime": "4.0.9", "@tarojs/shared": "4.0.9", "@tarojs/taro": "4.0.9", "clsx": "^2.1.1", "event-target-polyfill": "^0.0.4", "jotai": "^2.12.2", "jotai-immer": "^0.4.1", "md5": "^2.3.0", "miniprogram-sm-crypto": "^0.3.13", "react": "^18.0.0", "react-dom": "^18.0.0", "tailwind-merge": "^3.2.0", "wxmp-rsa": "^2.1.0", "yet-another-abortcontroller-polyfill": "^0.0.4", "zod": "^3.24.2"}, "devDependencies": {"@babel/core": "^7.24.4", "@babel/plugin-proposal-class-properties": "7.14.5", "@babel/plugin-transform-class-properties": "^7.25.9", "@babel/plugin-transform-private-methods": "^7.25.9", "@babel/plugin-transform-private-property-in-object": "^7.25.9", "@babel/preset-react": "^7.24.1", "@tailwindcss/postcss": "^4.0.15", "@tarojs/cli": "4.0.9", "@tarojs/vite-runner": "4.0.9", "@types/md5": "^2.3.5", "@types/react": "^18.0.0", "@vitejs/plugin-react": "^4.3.0", "babel-preset-taro": "4.0.9", "eslint": "^8.57.0", "eslint-config-prettier": "^10.1.1", "eslint-config-taro": "4.0.9", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.4.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "react-refresh": "^0.14.0", "stylelint": "^16.4.0", "tailwindcss": "^4.0.15", "terser": "^5.30.4", "typescript": "^5.4.5", "vite": "^4.2.0", "weapp-tailwindcss": "^4.1.0"}}