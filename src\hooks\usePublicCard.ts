import { useQuery } from '@tanstack/react-query';
import { getPublicCardInfo } from '../api/card';
import { tokenAtom } from '../store/token';
import { useAtomValue } from 'jotai';

export function usePublicCard(configId: string) {
  const token = useAtomValue(tokenAtom);
  const cardQuery = useQuery({
    queryKey: ['publicCard', configId],
    queryFn: () => getPublicCardInfo(configId, token),
    retry: 2, // 重试2次，给服务器一些处理时间
    retryDelay: 1000, // 重试间隔1秒
    staleTime: 0, // 始终认为数据是过时的，每次都重新获取
    refetchOnMount: true, // 组件挂载时重新获取数据
  });

  return cardQuery;
}
