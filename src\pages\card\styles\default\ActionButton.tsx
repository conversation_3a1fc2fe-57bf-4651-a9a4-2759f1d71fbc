import { Button, Text } from '@tarojs/components';
import {
  contactCardSVGSource,
  personAddSVGSource,
  phoneCallSVGSource,
  shareSVGSource,
  SVG,
} from '../../../../assets/icon';
import { useI18n } from '../../../../i18n/context';

type ActionButtonType = 'call' | 'addContact' | 'share' | 'myCard';

type ActionButtonProps = {
  type: ActionButtonType;
  onClick: () => void;
  className?: string;
};



export function ActionButton({ type, onClick, className }: ActionButtonProps) {
  const { t } = useI18n();

  const actionButtonMap: Record<
    ActionButtonType,
    {
      text: string;
      icon: string;
      color: string;
    }
  > = {
    call: { text: t.call, icon: phoneCallSVGSource, color: 'bg-green-500' },
    addContact: { text: t.addToContacts, icon: personAddSVGSource, color: 'bg-yellow-500' },
    share: { text: t.share, icon: shareSVGSource, color: 'bg-blue-500' },
    myCard: { text: t.myCard, icon: contactCardSVGSource, color: 'bg-purple-500' },
  };

  const { text, icon, color } = actionButtonMap[type];
  return (
    <Button
      className={`flex items-center justify-center rounded-xl ${color} w-full py-2.5 text-center ${className}`}
      onClick={onClick}
      openType={type === 'share' ? 'share' : undefined}
    >
      <SVG src={icon} color="#fff" size={24} />
      <Text className="ml-2 text-base text-white">{text}</Text>
    </Button>
  );
}
