import { Button, Text } from '@tarojs/components';
import {
  contactCardSVGSource,
  personAddSVGSource,
  phoneCallSVGSource,
  shareSVGSource,
  SVG,
} from '../../../../assets/icon';

type ActionButtonType = 'call' | 'addContact' | 'share' | 'myCard';

type ActionButtonProps = {
  type: ActionButtonType;
  onClick: () => void;
  className?: string;
};

const actionButtonMap: Record<
  ActionButtonType,
  {
    text: string;
    icon: string;
    color: string;
  }
> = {
  call: { text: '拨打电话', icon: phoneCallSVGSource, color: 'bg-green-500' },
  addContact: { text: '添加到通讯录', icon: personAddSVGSource, color: 'bg-yellow-500' },
  share: { text: '分享名片', icon: shareSVGSource, color: 'bg-blue-500' },
  myCard: { text: '我的名片', icon: contactCardSVGSource, color: 'bg-purple-500' },
};

export function ActionButton({ type, onClick, className }: ActionButtonProps) {
  const { text, icon, color } = actionButtonMap[type];
  return (
    <Button
      className={`flex items-center justify-center rounded-xl ${color} w-full py-2.5 text-center ${className}`}
      onClick={onClick}
      openType={type === 'share' ? 'share' : undefined}
    >
      <SVG src={icon} color="#fff" size={24} />
      <Text className="ml-2 text-base text-white">{text}</Text>
    </Button>
  );
}
