import { Button, Text, View } from '@tarojs/components';
import { useState } from 'react';
import Taro from '@tarojs/taro';
import LoginWithPhoneButton from '../../components/LoginWithPhoneButton';
import { MaterialCard, PageView } from '../../components';
import {
  getCardInfo,
  getCardConfigs,
  createCardConfig,
  updateCardConfig,
  deleteCardConfig,
  getPublicCardInfo,
} from '../../api';
import type { CardConfig } from '../../types';

export default function ApiTestPage() {
  const [token, setToken] = useState<string>('');
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  // 登录成功回调
  const handleLoginSuccess = (success: boolean) => {
    if (success) {
      console.log('登录成功，获取到token:', token);
      setToken(token);
      setIsLoggedIn(true);
      Taro.showToast({
        title: '登录成功',
        icon: 'success',
      });
    }
  };

  // 登录失败回调
  const handleLoginFailed = (error: string) => {
    console.error('登录失败:', error);
    Taro.showToast({
      title: '登录失败',
      icon: 'error',
    });
  };

  // 测试获取名片信息
  const testGetCardInfo = async () => {
    try {
      const cardInfo = await getCardInfo(token);
      console.log('获取名片信息成功:', cardInfo);
    } catch (error) {
      console.error('获取名片信息失败:', error);
    }
  };

  // 测试获取名片配置列表
  const testGetCardConfigs = async () => {
    try {
      const configs = await getCardConfigs(token);
      console.log('获取名片配置列表成功:', configs);
    } catch (error) {
      console.error('获取名片配置列表失败:', error);
    }
  };

  // 测试创建名片配置
  const testCreateCardConfig = async () => {
    try {
      // 预设的测试数据
      const newConfig: Partial<CardConfig> = {
        name: `测试配置-${new Date().toISOString().slice(0, 10)}`,
        styleName: 'default',
        showAvatar: true,
        showName: true,
        showDepartment: true,
        showPosition: true,
        showTitle: true,
        showCompany: true,
        showMobile: true,
        showTelephone: true,
        showEmail: true,
        showAddress: true,
        public: false,
      };

      const result = await createCardConfig(newConfig, token);
      console.log('创建名片配置成功:', result);
    } catch (error) {
      console.error('创建名片配置失败:', error);
    }
  };

  // 测试更新名片配置
  const testUpdateCardConfig = async () => {
    try {
      // 先获取配置列表
      const configs = await getCardConfigs(token);
      if (configs.length === 0) {
        console.log('没有可更新的配置，请先创建配置');
        Taro.showToast({
          title: '请先创建配置',
          icon: 'none',
        });
        return;
      }

      // 更新第一个配置
      const configToUpdate = configs[0];
      const updatedConfig: Partial<CardConfig> = {
        name: `更新配置-${new Date().toISOString().slice(0, 10)}`,
        public: !configToUpdate.public,
      };

      const result = await updateCardConfig(configToUpdate.id, updatedConfig, token);
      console.log('更新名片配置成功:', result);
    } catch (error) {
      console.error('更新名片配置失败:', error);
    }
  };

  // 测试删除名片配置
  const testDeleteCardConfig = async () => {
    try {
      // 先获取配置列表
      const configs = await getCardConfigs(token);
      if (configs.length === 0) {
        console.log('没有可删除的配置');
        Taro.showToast({
          title: '没有可删除的配置',
          icon: 'none',
        });
        return;
      }

      // 删除最后一个配置
      const configToDelete = configs[configs.length - 1];
      const result = await deleteCardConfig(configToDelete.id, token);
      console.log('删除名片配置成功:', result);
    } catch (error) {
      console.error('删除名片配置失败:', error);
    }
  };

  // 测试获取公开名片信息
  const testGetPublicCardInfo = async () => {
    try {
      // 先获取配置列表
      const configs = await getCardConfigs(token);
      const publicConfigs = configs.filter((config) => config.public);

      if (publicConfigs.length === 0) {
        console.log('没有公开的名片配置，请先创建并设置为公开');
        Taro.showToast({
          title: '没有公开配置',
          icon: 'none',
        });
        return;
      }

      // 获取第一个公开配置的信息
      const result = await getPublicCardInfo(publicConfigs[0].id);
      console.log('获取公开名片信息成功:', result);
    } catch (error) {
      console.error('获取公开名片信息失败:', error);
    }
  };

  return (
    <PageView>
      <View className="flex flex-col items-center justify-center p-4">
        <MaterialCard className="w-full p-4">
          <Text className="mb-4 block text-center text-lg font-bold">API 测试页面</Text>

          {!isLoggedIn ? (
            <View className="my-4 flex justify-center">
              <LoginWithPhoneButton
                className=""
                onLogin={handleLoginSuccess}
                onFailed={handleLoginFailed}
              />
            </View>
          ) : (
            <View className="my-2 flex justify-center">
              <Text className="text-green-500">已登录</Text>
            </View>
          )}

          {isLoggedIn && (
            <View className="mt-4 grid grid-cols-1 gap-2">
              <Button className="rounded-md bg-blue-500 text-white" onClick={testGetCardInfo}>
                获取名片信息
              </Button>

              <Button className="rounded-md bg-blue-500 text-white" onClick={testGetCardConfigs}>
                获取名片配置列表
              </Button>

              <Button className="rounded-md bg-green-500 text-white" onClick={testCreateCardConfig}>
                创建名片配置
              </Button>

              <Button
                className="rounded-md bg-yellow-500 text-white"
                onClick={testUpdateCardConfig}
              >
                更新名片配置
              </Button>

              <Button className="rounded-md bg-red-500 text-white" onClick={testDeleteCardConfig}>
                删除名片配置
              </Button>

              <Button
                className="rounded-md bg-purple-500 text-white"
                onClick={testGetPublicCardInfo}
              >
                获取公开名片信息
              </Button>
            </View>
          )}

          <Text className="mt-4 block text-center text-sm text-gray-500">
            所有API调用结果将在控制台中显示
          </Text>
        </MaterialCard>
      </View>
    </PageView>
  );
}
