/**
 * Info数据 Hook
 */
import { useQuery } from '@tanstack/react-query';
import { getInfo } from '../api/info';

/**
 * 获取Info数据的Hook
 * @param name info名称
 */
export function useInfo(name: string) {
  return useQuery({
    queryKey: ['info', name],
    queryFn: () => getInfo(name),
    staleTime: 1000 * 60 * 10, // 10分钟内数据不过期
    gcTime: 1000 * 60 * 30, // 30分钟缓存时间
    retry: 2, // 失败重试2次
    retryDelay: 1000, // 重试间隔1秒
  });
} 