import Taro from '@tarojs/taro';
import type { ApiResponse } from '../types';
import { decryptSM4, encryptSM4 } from '../utils/crypto';
import { SM4_KEY } from './api.config';
import { handleApiStatusCode } from './utils';

const isDev = process.env.NODE_ENV === 'development';

/**
 * API通信工具类
 * 自动处理加密/解密的API请求
 */
export class Api {
  private sm4Key: string;

  /**
   * 创建API通信工具实例
   * @param sm4Key SM4加密密钥，默认使用配置中的SM4_KEY
   */
  constructor(sm4Key: string = SM4_KEY) {
    this.sm4Key = sm4Key;
  }

  /**
   * 加密API请求数据
   * @param data 要加密的数据
   * @returns 加密后的数据对象
   */
  private encryptData<T>(data: T): { encrypted: string } {
    const jsonStr = JSON.stringify(data);
    const encrypted = encryptSM4(jsonStr, this.sm4Key);
    return { encrypted };
  }

  /**
   * 解密API响应数据
   * @param response 包含加密数据的响应
   * @returns 解密后的数据
   */
  private decryptResponse<T>(response: { encrypted: string }): T {
    if (!response || !response.encrypted) {
      return response as T;
    }

    const decrypted = decryptSM4(response.encrypted, this.sm4Key);
    try {
      return JSON.parse(decrypted) as T;
    } catch (error) {
      console.error('解析解密数据失败:', error);
      return decrypted as unknown as T;
    }
  }

  /**
   * 发送GET请求（自动解密响应）
   * @param url 请求URL
   * @param header 请求头
   * @returns 解密后的响应数据
   */
  async get<T>(url: string, token?: string): Promise<T | null> {
    // 发送请求
    const response = await Taro.request({
      url,
      method: 'GET',
      header: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
    });

    // 解密响应数据
    const decryptedResponse = this.decryptResponse<T>(response.data);

    if (isDev) {
      console.log('GET请求:', { url, response: decryptedResponse });
    }

    // 检查HTTP状态码
    if (response.statusCode !== 200) {
      const errorMsg = (decryptedResponse as ApiResponse<unknown>)?.msg || '获取数据失败';
      handleApiStatusCode(response.statusCode, errorMsg);
      return null;
    }

    return decryptedResponse;
  }

  /**
   * 发送POST请求（自动加密请求数据和解密响应）
   * @param url 请求URL
   * @param data 请求数据
   * @param header 请求头
   * @returns 解密后的响应数据
   */
  async post<T, D>(url: string, data: D, token?: string): Promise<T | null> {
    // 加密请求数据
    const encryptedData = this.encryptData(data);

    // 发送请求
    const response = await Taro.request({
      url,
      method: 'POST',
      data: encryptedData,
      header: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
    });

    // 解密响应数据
    const decryptedResponse = this.decryptResponse<T>(response.data);

    if (isDev) {
      console.log('POST请求:', { url, data, response: decryptedResponse });
    }

    // 检查HTTP状态码
    if (response.statusCode !== 200 && response.statusCode !== 201) {
      const errorMsg = (decryptedResponse as ApiResponse<unknown>)?.msg || '提交数据失败';
      handleApiStatusCode(response.statusCode, errorMsg);
      return null;
    }

    return decryptedResponse;
  }

  /**
   * 发送PATCH请求（自动加密请求数据和解密响应）
   * @param url 请求URL
   * @param data 请求数据
   * @param header 请求头
   * @returns 解密后的响应数据
   */
  async patch<T, D>(
    url: string,
    data: D,
    token?: string,
  ): Promise<T | null> {
    // 加密请求数据
    const encryptedData = this.encryptData(data);

    // 发送请求
    const response = await Taro.request({
      url,
      method: 'PATCH',
      data: encryptedData,
      header: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
    });

    // 解密响应数据
    const decryptedResponse = this.decryptResponse<T>(response.data);

    if (isDev) {
      console.log('PATCH请求:', { url, data, response: decryptedResponse });
    }

    // 检查HTTP状态码
    if (response.statusCode !== 200) {
      const errorMsg = (decryptedResponse as ApiResponse<unknown>)?.msg || '更新数据失败';
      handleApiStatusCode(response.statusCode, errorMsg);
      return null;
    }

    return decryptedResponse;
  }

  /**
   * 发送DELETE请求（自动解密响应）
   * @param url 请求URL
   * @param header 请求头
   * @returns 解密后的响应数据
   */
  async delete<T>(url: string, token?: string): Promise<T | null> {
    // 发送请求
    const response = await Taro.request({
      url,
      method: 'DELETE',
      header: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
    });

    // 解密响应数据
    const decryptedResponse = this.decryptResponse<T>(response.data);

    if (isDev) {
      console.log('DELETE请求:', { url, response: decryptedResponse });
    }

    // 检查HTTP状态码
    if (response.statusCode !== 200 && response.statusCode !== 204) {
      const errorMsg = (decryptedResponse as ApiResponse<unknown>)?.msg || '删除数据失败';
      handleApiStatusCode(response.statusCode, errorMsg);
      return null;
    }

    return decryptedResponse;
  }

  /**
   * 发送PUT请求（自动加密请求数据和解密响应）
   * @param url 请求URL
   * @param data 请求数据
   * @param header 请求头
   * @returns 解密后的响应数据
   */
  async put<T, D>(url: string, data: D, token?: string): Promise<T | null> {
    // 加密请求数据
    const encryptedData = this.encryptData(data);

    // 发送请求
    const response = await Taro.request({
      url,
      method: 'PUT',
      data: encryptedData,
      header: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
    });

    // 解密响应数据
    const decryptedResponse = this.decryptResponse<T>(response.data);

    if (isDev) {
      console.log('PUT请求:', { url, data, response: decryptedResponse });
    }

    // 检查HTTP状态码
    if (response.statusCode !== 200) {
      const errorMsg = (decryptedResponse as ApiResponse<unknown>)?.msg || '更新数据失败';
      handleApiStatusCode(response.statusCode, errorMsg);
      return null;
    }

    return decryptedResponse;
  }
}
