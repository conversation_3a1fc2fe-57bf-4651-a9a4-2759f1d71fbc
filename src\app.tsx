// Import polyfills first
import 'event-target-polyfill';
import 'yet-another-abortcontroller-polyfill';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useLaunch } from '@tarojs/taro';
import { Provider as <PERSON><PERSON>Provider } from 'jotai';
import type { PropsWithChildren } from 'react';

import { globalStore } from './store';
import './app.css';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1, // 失败重试次数
      staleTime: 1000 * 60 * 5, // 数据过期时间（5分钟）
      gcTime: 1000 * 60 * 30, // 缓存时间（30分钟）
    },
  },
});

function App({ children }: PropsWithChildren) {
  useLaunch(() => {
    console.log('App launched.');
  });

  // 使用QueryProvider包裹应用
  return (
    <QueryClientProvider client={queryClient}>
      <JotaiProvider store={globalStore}>{children}</JotaiProvider>
    </QueryClientProvider>
  );
}

export default App;
