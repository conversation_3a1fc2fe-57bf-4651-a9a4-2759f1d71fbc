/**
 * 名片相关API
 */
import Taro from '@tarojs/taro';
import type { ApiResponse, CardInfo } from '../types';
import { Api } from './api-crypto';
import { API_PATHS, BASE_URL } from './api.config';
import { withNavigationLoadingHOF } from './utils';

// API URL
const cardInfoUrl = BASE_URL + API_PATHS.CARD.INFO;

/**
 * 获取当前用户的名片信息
 * @returns 名片信息
 */
export const getCardInfo = withNavigationLoadingHOF(async (token: string): Promise<CardInfo> => {
  const api = new Api();
  try {
    const response = await api.get<ApiResponse<CardInfo>>(cardInfoUrl, token);

    if (!response?.data) {
      throw new Error(response?.msg || '获取名片信息失败');
    }

    return response.data;
  } catch (error) {
    console.error('获取名片信息失败:', error);
    Taro.showToast({
      title: '获取名片信息失败',
      icon: 'error',
    });
    throw error;
  }
});

export interface PublicCardInfoResponse {
  info: Partial<CardInfo>;
  style: string;
  public: boolean;
}

/**
 * 获取公开名片信息
 * @param id 配置ID
 * @returns 公开名片信息
 */
export const getPublicCardInfo = withNavigationLoadingHOF(
  async (id: string, token?: string): Promise<PublicCardInfoResponse> => {
    const api = new Api();
    const url = BASE_URL + API_PATHS.CARD.PUBLIC_INFO(id);

    try {
      const response = await api.get<ApiResponse<PublicCardInfoResponse>>(url, token);

      if (!response?.data) {
        throw new Error(response?.msg || '获取公开名片信息失败');
      }

      return response.data;
    } catch (error) {
      console.error('获取公开名片信息失败:', error);

      // 不在这里显示 Toast，让页面组件处理错误提示
      // 这样可以显示更友好的错误页面

      throw error;
    }
  },
);
