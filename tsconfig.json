{
  "compilerOptions": {
    "target": "esnext",
    "module": "NodeNext",
    "removeComments": false,
    "preserveConstEnums": true,
    "moduleResolution": "NodeNext",
    "experimentalDecorators": true,
    "noImplicitAny": false,
    "allowSyntheticDefaultImports": true,
    "outDir": "lib",
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "strictNullChecks": true,
    "sourceMap": true,
    "rootDir": ".",
    "jsx": "react-jsx",
    "allowJs": true,
    "resolveJsonModule": true,
    "typeRoots": [
      "node_modules/@types"
    ],
    // "paths": {
    //   // TS5090 leading './'
    //   "@/*": ["./src/*"]
    // }
  },
  "include": ["./src", "./types", "./config"],
  "compileOnSave": false
}
