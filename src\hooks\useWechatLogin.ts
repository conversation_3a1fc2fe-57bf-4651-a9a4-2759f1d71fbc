/**
 * 微信登录 Hook
 */
import { useMutation } from '@tanstack/react-query';
import Taro from '@tarojs/taro';
import { useAtom } from 'jotai';
import { useEffect } from 'react';
import { loginWithWechatPhone } from '../api/wechat';
import { tokenAtom } from '../store/token';

/**
 * 微信登录 Hook
 * 负责处理微信登录相关的功能
 */
export function useWechatLogin() {
  const [token, setToken] = useAtom(tokenAtom);
  const loginMutation = useMutation({ mutationFn: loginWithWechatPhone });

  // 检查是否已登录
  const isLoggedIn = !!token;

  useEffect(() => {
    if (loginMutation.isPending) {
      Taro.showLoading({ title: '登录中...' });
    } else {
      Taro.hideLoading();
    }
  }, [loginMutation.isPending]);

  // 登录
  const handleLogin = async (code: string): Promise<boolean> => {
    try {
      const loginResponse = await loginMutation.mutateAsync(code);
      // 保存登录状态到 Jotai atom (会自动同步到存储)
      console.log('🌏handleLogin', loginResponse);
      setToken(loginResponse.token);

      return true;
    } catch (error) {
      return false;
    }
  };

  // 登出
  const handleLogout = (): boolean => {
    try {
      // 清除token
      setToken('');

      return true;
    } catch (error) {
      console.error('退出登录失败：', error);
      return false;
    }
  };

  return {
    isLoggedIn,
    login: handleLogin,
    logout: handleLogout,
    isLoading: loginMutation.isPending,
  };
}
