import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useState, useEffect } from 'react';
import { i18n, type Language, supportedLanguages } from '../../i18n/index';

// 创建一个简单的地球图标
const globeIcon = `<svg viewBox="0 0 24 24" fill="currentColor">
  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
</svg>`;

interface LanguageSelectorProps {
  onLanguageChange?: (language: Language) => void;
  theme?: 'light' | 'red';
  className?: string;
}

export function LanguageSelector({ onLanguageChange, theme = 'light', className }: LanguageSelectorProps) {
  const [currentLanguage, setCurrentLanguage] = useState<Language>(i18n.getCurrentLanguage());

  const languages: { code: Language; name: string }[] = [
    { code: 'zh', name: i18n.t('language.chinese') },
    { code: 'en', name: i18n.t('language.english') },
    { code: 'ru', name: i18n.t('language.russian') },
  ];

  const handleLanguageSelect = () => {
    Taro.showActionSheet({
      itemList: languages.map(lang => lang.name),
      success: (res) => {
        const selectedLanguage = languages[res.tapIndex];
        if (selectedLanguage) {
          setCurrentLanguage(selectedLanguage.code);
          i18n.setLanguage(selectedLanguage.code);
          onLanguageChange?.(selectedLanguage.code);
          
          Taro.showToast({
            title: `${i18n.t('language.select')}: ${selectedLanguage.name}`,
            icon: 'none',
            duration: 1500,
          });
        }
      },
    });
  };

  // 根据主题选择样式
  const getThemeStyles = () => {
    if (theme === 'red') {
      return {
        container: 'flex items-center justify-center rounded-xl bg-gradient-to-r from-red-500 to-red-600 px-4 py-2 shadow-md hover:shadow-lg transition-all duration-200',
        text: 'ml-2 text-sm font-semibold text-white',
        iconColor: '#ffffff'
      };
    }
    return {
      container: 'flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500 to-blue-600 px-4 py-2 shadow-md hover:shadow-lg transition-all duration-200',
      text: 'ml-2 text-sm font-semibold text-white',
      iconColor: '#ffffff'
    };
  };

  const styles = getThemeStyles();

  return (
    <View
      className={`${styles.container} ${className || ''}`}
      onClick={handleLanguageSelect}
      style={{
        minHeight: '20px',
        minWidth: '40px',
        cursor: 'pointer',
        transform: 'scale(1)',
        transition: 'transform 0.1s ease-in-out',
      }}
      hoverClass="scale-105"
      hoverStayTime={100}
    >
      <View
        style={{
          width: '18px',
          height: '18px',
          display: 'inline-block',
        }}
        dangerouslySetInnerHTML={{
          __html: globeIcon.replace(/fill="[^"]*"/g, `fill="${styles.iconColor}"`),
        }}
      />
      <Text className={styles.text}>
        {currentLanguage.toUpperCase()}
      </Text>
    </View>
  );
}





