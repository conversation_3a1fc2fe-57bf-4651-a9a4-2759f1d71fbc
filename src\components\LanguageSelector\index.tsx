import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useState } from 'react';
import { i18n, type Language } from '../../i18n';
import { SVG, globeSVGSource } from '../../assets/icon';

interface LanguageSelectorProps {
  onLanguageChange?: (language: Language) => void;
  theme?: 'light' | 'red';
  className?: string;
}

export function LanguageSelector({ onLanguageChange, theme = 'light', className }: LanguageSelectorProps) {
  const [currentLanguage, setCurrentLanguage] = useState<Language>(i18n.getCurrentLanguage());

  const languages: { code: Language; name: string }[] = [
    { code: 'zh', name: i18n.t('language.chinese') },
    { code: 'en', name: i18n.t('language.english') },
    { code: 'ru', name: i18n.t('language.russian') },
  ];

  const handleLanguageSelect = () => {
    Taro.showActionSheet({
      itemList: languages.map(lang => lang.name),
      success: (res) => {
        const selectedLanguage = languages[res.tapIndex];
        if (selectedLanguage) {
          setCurrentLanguage(selectedLanguage.code);
          i18n.setLanguage(selectedLanguage.code);
          onLanguageChange?.(selectedLanguage.code);
          
          Taro.showToast({
            title: `${i18n.t('language.select')}: ${selectedLanguage.name}`,
            icon: 'none',
            duration: 1500,
          });
        }
      },
    });
  };

  // 根据主题选择样式
  const getThemeStyles = () => {
    if (theme === 'red') {
      return {
        container: 'flex items-center justify-center rounded-xl bg-gradient-to-r from-red-500 to-red-600 px-4 py-2 shadow-md hover:shadow-lg transition-all duration-200',
        text: 'ml-2 text-sm font-semibold text-white',
        iconColor: '#ffffff'
      };
    }
    return {
      container: 'flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500 to-blue-600 px-4 py-2 shadow-md hover:shadow-lg transition-all duration-200',
      text: 'ml-2 text-sm font-semibold text-white',
      iconColor: '#ffffff'
    };
  };

  const styles = getThemeStyles();

  return (
    <View
      className={`${styles.container} ${className || ''}`}
      onClick={handleLanguageSelect}
      style={{
        minHeight: '40px',
        minWidth: '80px',
        cursor: 'pointer',
        transform: 'scale(1)',
        transition: 'transform 0.1s ease-in-out',
      }}
      hoverClass="scale-105"
      hoverStayTime={100}
    >
      <SVG src={globeSVGSource} size={18} color={styles.iconColor} />
      <Text className={styles.text}>
        {currentLanguage.toUpperCase()}
      </Text>
    </View>
  );
}





