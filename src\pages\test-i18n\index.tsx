import { View, Text, Button } from '@tarojs/components';
import { useI18n } from '../../i18n/context';
import { LanguageSelector } from '../../components/LanguageSelector';
import { useState } from 'react';

export default function TestI18nPage() {
  const { t, language, translateText } = useI18n();
  const [translatedText, setTranslatedText] = useState('');
  const [isTranslating, setIsTranslating] = useState(false);

  const handleTestTranslation = async () => {
    setIsTranslating(true);
    try {
      const result = await translateText('这是一个测试文本');
      setTranslatedText(result);
    } catch (error) {
      console.error('Translation failed:', error);
      setTranslatedText('翻译失败');
    } finally {
      setIsTranslating(false);
    }
  };

  return (
    <View className="p-4">
      <Text className="text-xl font-bold mb-4">国际化功能测试</Text>
      
      {/* 语言选择器 */}
      <View className="mb-6">
        <Text className="text-lg mb-2">语言选择：</Text>
        <LanguageSelector theme="red" />
      </View>

      {/* 静态翻译测试 */}
      <View className="mb-6">
        <Text className="text-lg mb-2">静态翻译测试：</Text>
        <View className="bg-gray-100 p-3 rounded">
          <Text>当前语言: {language}</Text>
          <Text>电子名片: {t.businessCard}</Text>
          <Text>手机: {t.mobile}</Text>
          <Text>邮箱: {t.email}</Text>
          <Text>公司: {t.company}</Text>
          <Text>联系人: {t.contactPerson}</Text>
        </View>
      </View>

      {/* 动态翻译测试 */}
      <View className="mb-6">
        <Text className="text-lg mb-2">动态翻译测试：</Text>
        <Button 
          onClick={handleTestTranslation}
          disabled={isTranslating}
          className="mb-2"
        >
          {isTranslating ? '翻译中...' : '测试翻译API'}
        </Button>
        {translatedText && (
          <View className="bg-blue-100 p-3 rounded">
            <Text>翻译结果: {translatedText}</Text>
          </View>
        )}
      </View>

      {/* 操作按钮翻译测试 */}
      <View className="mb-6">
        <Text className="text-lg mb-2">操作按钮翻译：</Text>
        <View className="bg-gray-100 p-3 rounded">
          <Text>拨打电话: {t.call}</Text>
          <Text>添加到通讯录: {t.addToContacts}</Text>
          <Text>分享名片: {t.share}</Text>
          <Text>我的名片: {t.myCard}</Text>
          <Text>关注: {t.follow}</Text>
        </View>
      </View>

      {/* 错误信息翻译测试 */}
      <View className="mb-6">
        <Text className="text-lg mb-2">错误信息翻译：</Text>
        <View className="bg-red-100 p-3 rounded">
          <Text>获取名片信息失败: {t.getCardInfoFailed}</Text>
          <Text>图片加载失败: {t.imageLoadFailed}</Text>
          <Text>此名片未公开: {t.cardNotPublic}</Text>
        </View>
      </View>
    </View>
  );
}
