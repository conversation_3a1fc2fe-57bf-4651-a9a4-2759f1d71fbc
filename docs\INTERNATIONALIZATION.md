# 国际化功能使用指南

本项目已集成完整的国际化功能，支持中文、英文、俄文三种语言，并集成了百度翻译API用于动态内容翻译。

## 功能特性

- ✅ 支持中文、英文、俄文三种语言
- ✅ 语言切换按钮，用户可随时切换语言
- ✅ 静态文本国际化（界面标签、按钮文字等）
- ✅ 动态内容翻译（名片信息等用户数据）
- ✅ 百度翻译API集成
- ✅ 语言设置本地存储
- ✅ 响应式设计，适配不同语言文本长度

## 使用方法

### 1. 语言切换

在名片页面右上角有语言切换按钮，点击可选择语言：
- 🇨🇳 中文
- 🇺🇸 English  
- 🇷🇺 Русский

### 2. 在组件中使用翻译

```tsx
import { useI18n } from '../i18n/context';

function MyComponent() {
  const { t, language, translateText } = useI18n();
  
  return (
    <View>
      {/* 使用静态翻译 */}
      <Text>{t.businessCard}</Text>
      
      {/* 使用动态翻译 */}
      <Text>{await translateText('动态内容', language)}</Text>
    </View>
  );
}
```

### 3. 添加新的翻译文本

在 `src/i18n/index.ts` 中添加新的翻译键：

```typescript
export interface TranslationKeys {
  // 添加新的翻译键
  newKey: string;
}

export const zhTranslations: TranslationKeys = {
  newKey: '中文文本',
};

export const enTranslations: TranslationKeys = {
  newKey: 'English text',
};

export const ruTranslations: TranslationKeys = {
  newKey: 'Русский текст',
};
```

## 百度翻译API配置

### 1. 获取API密钥

1. 访问 [百度翻译开放平台](https://fanyi-api.baidu.com/)
2. 注册账号并创建应用
3. 获取 APPID 和密钥

### 2. 配置API密钥

编辑 `src/config/translate.ts` 文件：

```typescript
export const BAIDU_TRANSLATE_CONFIG = {
  appid: 'your_actual_appid',     // 替换为您的APPID
  appkey: 'your_actual_appkey',   // 替换为您的密钥
  apiUrl: 'https://fanyi-api.baidu.com/api/trans/vip/translate',
};
```

### 3. 环境变量配置（推荐）

创建 `.env.local` 文件：

```
BAIDU_TRANSLATE_APPID=your_appid
BAIDU_TRANSLATE_APPKEY=your_appkey
```

## 技术实现

### 架构设计

```
src/i18n/
├── index.ts          # 翻译文本定义
├── context.tsx       # React Context 和 Hooks
└── 

src/api/
└── translate.ts      # 百度翻译API调用

src/config/
└── translate.ts      # 翻译API配置

src/components/
└── LanguageSelector/ # 语言切换组件
```

### 核心组件

1. **I18nProvider**: 提供国际化上下文
2. **useI18n Hook**: 获取翻译函数和当前语言
3. **LanguageSelector**: 语言切换组件
4. **translateObject**: 批量翻译对象字段

### 翻译流程

1. 用户切换语言
2. 静态文本立即更新
3. 动态内容调用百度翻译API
4. 翻译结果缓存到组件状态
5. 界面重新渲染显示翻译内容

## 注意事项

### 开发环境

- 可以使用示例配置进行开发测试
- 建议使用环境变量管理API密钥
- 注意API调用频率限制

### 生产环境

- **重要**: 不要在前端暴露真实的API密钥
- 建议通过后端代理调用翻译API
- 实现翻译结果缓存以减少API调用
- 考虑实现离线翻译或预翻译

### 性能优化

- 翻译结果会缓存在组件状态中
- 相同语言切换不会重复翻译
- 建议为常用内容预设翻译

## 故障排除

### 常见问题

1. **翻译不生效**
   - 检查API密钥配置
   - 查看控制台错误信息
   - 确认网络连接

2. **语言切换无效**
   - 检查I18nProvider是否正确包裹应用
   - 确认组件使用了useI18n Hook

3. **翻译API错误**
   - 检查API密钥是否正确
   - 确认账户余额充足
   - 查看API调用频率是否超限

### 调试方法

开启调试日志：

```typescript
console.log('🌍 Current language:', language);
console.log('🌍 Translation result:', translatedText);
```

## 扩展功能

### 添加新语言

1. 在 `Language` 类型中添加新语言代码
2. 在 `translations` 对象中添加翻译
3. 在 `supportedLanguages` 数组中添加语言信息
4. 更新百度翻译API的语言映射

### 自定义翻译服务

可以替换百度翻译API为其他翻译服务：

```typescript
// 实现自定义翻译函数
export async function customTranslate(text: string, from: string, to: string) {
  // 调用其他翻译API
}
```

## 许可证

本国际化功能基于项目许可证开源。
