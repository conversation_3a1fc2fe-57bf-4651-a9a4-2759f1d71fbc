import { Image, ScrollView, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useEffect, useState } from 'react';

import type { CardStyleProps } from '..';
import { addToContacts, makePhoneCall, shareCard } from '../../../../ability';
import { copyToClipboard } from '../../../../ability/clipboard';
import { useI18n } from '../../../../i18n/context';
import { translateObject } from '../../../../api/translate';

import { BottomCard } from '../../../../components/Intro/bottomCard';
import { ActionButton } from './ActionButton';
import { ScrollToTop } from './ScrollToTop';

import {
  SVG,
  callGraySVGSource,
  contactCardSVGSource,
  locationGraySVGSource,
  mailGraySVGSource,
  smartphoneGraySVGSource,
} from '../../../../assets/icon';
import arrowUpDouble from '../../../../assets/icon/arrowUpDouble.svg';
import logo from '../../../../assets/logo/logo_cn_with_en_horizental_red.svg';
import logo_wx from '../../../../assets/logo/logo_wx.jpg';
import oil from '../../../../assets/logo/oil.png';
import defaultAvatar from '../../../../assets/placeholder_avatar.jpg';

import { useWechatLogin } from '../../../../hooks';

export default function CardPage({ data }: CardStyleProps) {
  const { t, language } = useI18n();
  const [translatedData, setTranslatedData] = useState(data);

  useEffect(() => {
    Taro.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#c10007',
    });
  }, []);

  // 翻译数据
  useEffect(() => {
    const translateCardData = async () => {
      if (language === 'zh') {
        setTranslatedData(data);
        return;
      }

      try {
        const fieldsToTranslate = ['name', 'position', 'title', 'department', 'company', 'address'] as (keyof typeof data)[];
        const translated = await translateObject(data, fieldsToTranslate, 'zh', language);
        setTranslatedData(translated);
      } catch (error) {
        console.error('Translation failed:', error);
        setTranslatedData(data);
      }
    };

    translateCardData();
  }, [data, language]);

  const { isLoggedIn } = useWechatLogin();
  const [showScrollToTop, setShowScrollToTop] = useState(false);
  const [imageError, setImageError] = useState<{ error: boolean; message: string }>({
    error: false,
    message: '',
  });

  const handleClickMyCard = () => {
    if (isLoggedIn) {
      Taro.navigateTo({
        url: '/pages/card/manage',
      });
    } else {
      Taro.navigateTo({
        url: '/pages/index/index',
      });
    }
  };

  const getContactItemView = (
    dataItem: string | undefined,
    title: string,
    showCallButton: boolean = true,
    icon: string | null = null,
  ) => {
    return (
      dataItem && (
        <View className="flex flex-col items-start justify-between py-2">
          <View className="flex items-center">
            <SVG src={icon || ''} color="#666" size={13} className="h-full" />
            <Text className="ml-2 text-sm font-normal tracking-wide text-gray-600">{title}</Text>
          </View>
          <View className="mt-1 flex w-full items-center justify-between">
            <Text className="text-base font-normal tracking-wider text-gray-700">{dataItem}</Text>
            <View className="flex items-center">
              {showCallButton && (
                <View className="mr-2 rounded-md" onClick={() => makePhoneCall(dataItem!)}>
                  <Text className="text-sm font-normal text-red-800">立即联系</Text>
                </View>
              )}
              <View
                className="ml-4 rounded-md"
                onClick={() => copyToClipboard(data.mobile!, '手机')}
              >
                <Text className="text-sm font-normal text-gray-400">复制</Text>
              </View>
            </View>
          </View>
        </View>
      )
    );
  };

  return (
    <ScrollView
      id="scrollview"
      className="flex w-full flex-col justify-center"
      scrollY
      scrollWithAnimation
      upperThreshold={100}
      onScrollToUpper={() => {
        console.log('scroll to top');
        setShowScrollToTop(true);
      }}
      enableFlex
      enableBackToTop
    >
      <View className="relative flex min-h-[calc(100vh-160rpx)] flex-col bg-[#F7F7F7]">
        <View className="absolute top-0 right-0 left-0 h-60 bg-linear-to-b from-[#c10007] to-[#F7F7F7]"></View>

        {/* 顶部标题栏 */}
        <View className="flex h-5"></View>

        {/* 主卡片内容 */}
        <View className="relative mx-4 rounded-lg bg-white p-4">
          {/* 公司标识 */}
          <View className="mb-6 flex items-center justify-center">
            {/* <Image src={oil} className="h-4 w-4" mode="aspectFit" /> */}
            <Image src={logo} className="ml-1 h-4 w-32" mode="aspectFit" />
          </View>

          {/* 头像和基本信息 */}
          <View className="flex items-end">
            <View className="mr-4">
              <Image
                src={data.avatar || defaultAvatar}
                className="h-30 w-22 rounded-md"
                mode="aspectFill"
                webp={true}
                lazyLoad={false}
                onError={(e) => {
                  console.error('头像加载错误:', e.detail);
                  setImageError({
                    error: true,
                    message: `加载失败: ${e.detail?.errMsg || '未知错误'}, 图片路径: ${data.avatar || '使用默认头像'}`,
                  });
                  Taro.showToast({
                    title: '头像加载失败',
                    icon: 'none',
                    duration: 2000,
                  });
                }}
                onLoad={() => {
                  setImageError({ error: false, message: '' });
                }}
              />
              {imageError.error && (
                <View className="bg-opacity-70 absolute right-0 bottom-0 left-0 bg-red-500 p-1 text-center">
                  <Text className="text-xs text-white">{t.imageLoadFailed}</Text>
                </View>
              )}
            </View>

            <View className="flex flex-1 flex-col">
              <Text className="text-xl font-normal tracking-wider">{translatedData.name || t.contactPerson}</Text>
              {translatedData.company && (
                <View className="flex items-center my-1.5">
                  <Image src={oil} className="h-4 w-4 mr-1" mode="aspectFit" />
                  <Text className="text-[28rpx] text-gray-700">{translatedData.company}</Text>
                </View>
              )}
              {translatedData.position && (
                <View className="mb-1 flex items-center">
                  <Text className="text-sm text-gray-600">{t.position}：{translatedData.position}</Text>
                </View>
              )}
              {translatedData.title && (
                <View className="mb-1 flex items-center">
                  <Text className="text-sm text-gray-600">{t.title}：{translatedData.title}</Text>
                </View>
              )}
              {translatedData.department && (
                <View className="flex items-center">
                  <Text className="text-sm text-gray-600">{t.department}：{translatedData.department}</Text>
                </View>
              )}
            </View>

            {/* 右侧我的名片按钮 */}
            <View
              className="absolute top-4 right-4 flex flex-col items-center"
              onClick={handleClickMyCard}
            >
              <View className="flex h-9 w-9 items-center justify-center rounded-[100rpx] bg-red-700">
                <SVG src={contactCardSVGSource} color="#fff" size={24} />
              </View>
              <Text className="mt-1 text-xs font-light text-red-700">{t.myCard}</Text>
            </View>
          </View>

          {/* 联系方式 */}
          <View className="mt-4">
            {getContactItemView(data.mobile, t.mobile, true, smartphoneGraySVGSource)}
            {getContactItemView(data.telephone, t.telephone, false, callGraySVGSource)}
            {getContactItemView(data.email, t.email, false, mailGraySVGSource)}
            {getContactItemView(translatedData.address, t.officeAddress, false, locationGraySVGSource)}
          </View>

          {/* 底部按钮 */}
          <View className="mt-6 flex gap-4">
            <View className="flex-1">
              <ActionButton type="share" onClick={() => shareCard(data)} />
            </View>
            <View className="flex-1">
              <ActionButton type="addContact" onClick={() => addToContacts(data)} />
            </View>
          </View>
        </View>

        {/* 公司底部信息 */}
        <View className="mx-4 mt-4 flex flex-row items-center justify-between rounded-lg bg-white px-4 py-2">
          <View className="flex items-center">
            <Image src={logo_wx} className="h-10 w-10 rounded-[100rpx]" mode="aspectFit" />
            <View className="ml-2 flex flex-col">
              <Text className="text-sm text-black">中石油吉林化工工程有限公司</Text>
              <Text className="text-xs font-light text-gray-400">{t.followOfficialAccount}</Text>
            </View>
          </View>
          <View
            className="flex items-center justify-center rounded-[100rpx] bg-red-700 px-3 py-1"
            onClick={() => Taro.navigateTo({ url: '/pages/qrcode/index' })}
          >
            <Text className="text-sm text-gray-200">{t.follow}</Text>
          </View>
        </View>
      </View>
      {/* 底部滑动提示 */}
      <View className="z-10 flex w-full items-center justify-center bg-[#F7F7F7] pt-2">
        <SVG src={arrowUpDouble} className="h-10 w-10" />
      </View>
      <BottomCard />
      {showScrollToTop && <ScrollToTop />}
    </ScrollView>
  );
}
