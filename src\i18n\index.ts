/**
 * 国际化配置和工具函数
 */

export type Language = 'zh' | 'en' | 'ru';

export interface TranslationKeys {
  // 通用
  loading: string;
  error: string;
  confirm: string;
  cancel: string;
  
  // 名片页面
  businessCard: string;
  cardNotPublic: string;
  cardNotPublicMessage: string;
  getCardInfoFailed: string;
  getCardInfoFailedMessage: string;
  contactPerson: string;
  
  // 联系信息标签
  mobile: string;
  telephone: string;
  email: string;
  address: string;
  officeAddress: string;
  position: string;
  title: string;
  department: string;
  company: string;
  
  // 操作按钮
  call: string;
  addToContacts: string;
  share: string;
  myCard: string;
  contact: string;
  saveToContacts: string;
  oneClickShare: string;
  follow: string;
  
  // 公司信息
  companyIntro: string;
  swipeUpForCompanyIntro: string;
  followOfficialAccount: string;
  latestNewsHere: string;
  
  // 图片加载
  imageLoadFailed: string;
  
  // 语言选择
  selectLanguage: string;
  chinese: string;
  english: string;
  russian: string;
}

// 默认中文翻译
export const zhTranslations: TranslationKeys = {
  // 通用
  loading: '加载中...',
  error: '错误',
  confirm: '确认',
  cancel: '取消',
  
  // 名片页面
  businessCard: '电子名片',
  cardNotPublic: '此名片未公开，无法分享',
  cardNotPublicMessage: '此名片未公开，无法分享',
  getCardInfoFailed: '获取名片信息失败',
  getCardInfoFailedMessage: '无法获取名片信息，请联系名片提供者',
  contactPerson: '联系人',
  
  // 联系信息标签
  mobile: '手机',
  telephone: '座机',
  email: '邮箱',
  address: '地址',
  officeAddress: '办公地址',
  position: '职务',
  title: '职位',
  department: '部门',
  company: '公司',
  
  // 操作按钮
  call: '拨打电话',
  addToContacts: '添加到通讯录',
  share: '分享名片',
  myCard: '我的名片',
  contact: '立即联系',
  saveToContacts: '存入通讯录',
  oneClickShare: '一键分享',
  follow: '关注',
  
  // 公司信息
  companyIntro: '公司介绍',
  swipeUpForCompanyIntro: '上划查看公司介绍',
  followOfficialAccount: '关注公众号，最新资讯都在这里',
  latestNewsHere: '最新资讯都在这里',
  
  // 图片加载
  imageLoadFailed: '图片加载失败',
  
  // 语言选择
  selectLanguage: '选择语言',
  chinese: '中文',
  english: 'English',
  russian: 'Русский',
};

// 英文翻译
export const enTranslations: TranslationKeys = {
  // 通用
  loading: 'Loading...',
  error: 'Error',
  confirm: 'Confirm',
  cancel: 'Cancel',

  // 名片页面
  businessCard: 'Business Card',
  cardNotPublic: 'This card is not public and cannot be shared',
  cardNotPublicMessage: 'This card is not public and cannot be shared',
  getCardInfoFailed: 'Failed to get card information',
  getCardInfoFailedMessage: 'Unable to get card information, please contact the card provider',
  contactPerson: 'Contact',

  // 联系信息标签
  mobile: 'Mobile',
  telephone: 'Phone',
  email: 'Email',
  address: 'Address',
  officeAddress: 'Office Address',
  position: 'Position',
  title: 'Title',
  department: 'Department',
  company: 'Company',

  // 操作按钮
  call: 'Call',
  addToContacts: 'Add to Contacts',
  share: 'Share Card',
  myCard: 'My Card',
  contact: 'Contact Now',
  saveToContacts: 'Save to Contacts',
  oneClickShare: 'Share',
  follow: 'Follow',

  // 公司信息
  companyIntro: 'Company Introduction',
  swipeUpForCompanyIntro: 'Swipe up to view company introduction',
  followOfficialAccount: 'Follow our official account for latest news',
  latestNewsHere: 'Latest news here',

  // 图片加载
  imageLoadFailed: 'Image load failed',

  // 语言选择
  selectLanguage: 'Select Language',
  chinese: '中文',
  english: 'English',
  russian: 'Русский',
};

// 俄文翻译
export const ruTranslations: TranslationKeys = {
  // 通用
  loading: 'Загрузка...',
  error: 'Ошибка',
  confirm: 'Подтвердить',
  cancel: 'Отмена',

  // 名片页面
  businessCard: 'Визитная карточка',
  cardNotPublic: 'Эта карточка не является публичной и не может быть передана',
  cardNotPublicMessage: 'Эта карточка не является публичной и не может быть передана',
  getCardInfoFailed: 'Не удалось получить информацию о карточке',
  getCardInfoFailedMessage: 'Невозможно получить информацию о карточке, обратитесь к поставщику карточки',
  contactPerson: 'Контакт',

  // 联系信息标签
  mobile: 'Мобильный',
  telephone: 'Телефон',
  email: 'Электронная почта',
  address: 'Адрес',
  officeAddress: 'Адрес офиса',
  position: 'Должность',
  title: 'Звание',
  department: 'Отдел',
  company: 'Компания',

  // 操作按钮
  call: 'Позвонить',
  addToContacts: 'Добавить в контакты',
  share: 'Поделиться карточкой',
  myCard: 'Моя карточка',
  contact: 'Связаться сейчас',
  saveToContacts: 'Сохранить в контакты',
  oneClickShare: 'Поделиться',
  follow: 'Подписаться',

  // 公司信息
  companyIntro: 'О компании',
  swipeUpForCompanyIntro: 'Проведите вверх, чтобы просмотреть информацию о компании',
  followOfficialAccount: 'Подпишитесь на наш официальный аккаунт для получения последних новостей',
  latestNewsHere: 'Последние новости здесь',

  // 图片加载
  imageLoadFailed: 'Ошибка загрузки изображения',

  // 语言选择
  selectLanguage: 'Выберите язык',
  chinese: '中文',
  english: 'English',
  russian: 'Русский',
};

export const translations = {
  zh: zhTranslations,
  en: enTranslations,
  ru: ruTranslations,
};

export const supportedLanguages: { code: Language; name: string; nativeName: string }[] = [
  { code: 'zh', name: 'Chinese', nativeName: '中文' },
  { code: 'en', name: 'English', nativeName: 'English' },
  { code: 'ru', name: 'Russian', nativeName: 'Русский' },
];

// 兼容旧版本的i18n对象
class I18nManager {
  private currentLanguage: Language = 'zh';
  private storageKey = 'app_language';

  constructor() {
    this.loadLanguageFromStorage();
  }

  private async loadLanguageFromStorage() {
    try {
      const { getStorage } = await import('@tarojs/taro');
      const result = await getStorage({ key: this.storageKey });
      if (result.data && ['zh', 'en', 'ru'].includes(result.data)) {
        this.currentLanguage = result.data as Language;
      }
    } catch (error) {
      // 使用默认语言
    }
  }

  getCurrentLanguage(): Language {
    return this.currentLanguage;
  }

  async setLanguage(language: Language) {
    this.currentLanguage = language;
    try {
      const { setStorage } = await import('@tarojs/taro');
      await setStorage({ key: this.storageKey, data: language });
    } catch (error) {
      console.error('Failed to save language:', error);
    }
  }

  t(key: string): string {
    const keys = key.split('.');
    const currentTranslations = translations[this.currentLanguage];

    // 处理嵌套的翻译键
    if (keys.length === 2 && keys[0] === 'language') {
      switch (keys[1]) {
        case 'chinese':
          return currentTranslations.chinese;
        case 'english':
          return currentTranslations.english;
        case 'russian':
          return currentTranslations.russian;
        case 'select':
          return currentTranslations.selectLanguage;
        default:
          return key;
      }
    }

    // 直接访问翻译键
    return (currentTranslations as any)[key] || key;
  }
}

export const i18n = new I18nManager();
