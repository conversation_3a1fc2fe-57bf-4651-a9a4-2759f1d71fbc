import Taro from '@tarojs/taro';
import type { CardInfo } from '../types';
import { isH5, isWeapp } from './platform';

/**
 * 添加到通讯录
 * @param cardInfo 名片信息
 */
export const addToContacts = (cardInfo: Partial<CardInfo>) => {
  if (isWeapp) {
    Taro.addPhoneContact({
      firstName: cardInfo.name ?? '',
      mobilePhoneNumber: cardInfo.mobile ?? '',
      workPhoneNumber: cardInfo.telephone ?? '',
      email: cardInfo.email ?? '',
      organization: cardInfo.company ?? '',
      title: `${cardInfo.department} ${cardInfo.title}`,
      workAddressStreet: cardInfo.address ?? '',
      success: () => {
        Taro.showToast({
          title: '已添加到通讯录',
          icon: 'success',
        });
      },
      fail: (err) => {
        console.error('添加到通讯录失败', err);
        Taro.showToast({
          title: '添加到通讯录失败',
          icon: 'error',
        });
      },
    });
  }
  if (isH5) {
    // 使用 vcard 格式
    const vcard = `BEGIN:VCARD
VERSION:3.0
N:${cardInfo.name}
TEL;TYPE=work,voice:${cardInfo.mobile}
EMAIL;TYPE=work:${cardInfo.email}
ORG:${cardInfo.company}
TITLE:${cardInfo.department} ${cardInfo.title}
`;
    // 使用 a 标签下载
    const a = document.createElement('a');
    a.href = `data:text/x-vcard;charset=utf-8,${encodeURIComponent(vcard)}`;
    a.download = `${cardInfo.name}.vcf`;
    a.click();
  }
};
