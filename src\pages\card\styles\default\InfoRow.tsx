import { View, Text } from '@tarojs/components';
import { copyToClipboard } from '../../../../ability/clipboard';

interface InfoRowProps {
  label: string;
  value: string;
  border?: 'top' | 'bottom' | 'both' | 'none';
}

function CopyButton({ value, label }: Pick<InfoRowProps, 'value' | 'label'>) {
  return (
    <View
      className="flex items-center justify-between rounded-2xl bg-gray-100 px-2 py-1"
      onClick={() => copyToClipboard(value, label)}
    >
      <Text className="text-xs text-gray-500">复制</Text>
    </View>
  );
}

export function InfoRow({ label, value, border = 'bottom' }: InfoRowProps) {
  const hasTopBorder = border === 'top' || border === 'both';
  const hasBottomBorder = border === 'bottom' || border === 'both';

  return (
    <View
      className={`flex flex-row items-center justify-between p-2 ${
        hasTopBorder && 'border-t-1 border-gray-200'
      } ${hasBottomBorder && 'border-b-1 border-gray-200'}`}
    >
      <View className="flex flex-col items-start justify-between">
        <Text className="text-xs text-gray-500">{label}</Text>
        <Text className="text-base">{value}</Text>
      </View>
      <CopyButton value={value} label={label} />
    </View>
  );
}
