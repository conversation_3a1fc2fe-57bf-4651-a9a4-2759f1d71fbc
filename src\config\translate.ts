/**
 * 百度翻译API配置
 * 
 * 使用说明：
 * 1. 注册百度翻译开放平台账号：https://fanyi-api.baidu.com/
 * 2. 创建应用获取 APPID 和密钥
 * 3. 将下面的配置替换为您的实际配置
 */

export const BAIDU_TRANSLATE_CONFIG = {
  // 请替换为您的实际APPID
  appid: '20250803002422981',
  
  // 请替换为您的实际密钥
  appkey: 'W6DEBQxVdtkzfHdRyHp8',
  
  // API地址（通常不需要修改）
  apiUrl: 'https://fanyi-api.baidu.com/api/trans/vip/translate',
};

// 语言代码映射
export const LANGUAGE_MAP = {
  zh: 'zh',
  en: 'en',
  ru: 'ru',
} as const;

/**
 * 开发环境配置说明：
 * 
 * 在开发环境中，您可以：
 * 1. 使用环境变量来配置API密钥
 * 2. 创建 .env.local 文件并添加：
 *    BAIDU_TRANSLATE_APPID=your_appid
 *    BAIDU_TRANSLATE_APPKEY=your_appkey
 * 
 * 生产环境配置说明：
 * 
 * 在生产环境中，建议：
 * 1. 使用服务器端代理来调用百度翻译API
 * 2. 避免在前端暴露API密钥
 * 3. 实现缓存机制以减少API调用次数
 */

// 如果有环境变量，优先使用环境变量
export const getTranslateConfig = () => {
  return {
    appid: process.env.BAIDU_TRANSLATE_APPID || BAIDU_TRANSLATE_CONFIG.appid,
    appkey: process.env.BAIDU_TRANSLATE_APPKEY || BAIDU_TRANSLATE_CONFIG.appkey,
    apiUrl: BAIDU_TRANSLATE_CONFIG.apiUrl,
  };
};
