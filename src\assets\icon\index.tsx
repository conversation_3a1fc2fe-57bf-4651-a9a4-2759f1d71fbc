import { Image, type ImageProps } from '@tarojs/components';

import contactCard from './contactCard.svg';
import personAdd from './personAdd.svg';
import phoneCall from './phoneCall.svg';
import share from './share.svg';
import arrowUp from './arrowUp.svg';
import arrowUpDouble from './arrowUpDouble.svg';
import check from './check.svg';
import cancel from './cancel.svg';
import exclamation from './exclamation.svg';
import lock from './lock.svg';
import rss from './rss.svg';
import smartphone from './smartphone.svg';
import qrcode from './qrcode.svg';
import call_gray from './call_gray_600.svg';
import smartphone_gray from './smartphone_gray_600.svg';
import mail_gray from './mail_gray_600.svg';
import location_gray from './location_gray_600.svg';

export const contactCardSVGSource = contactCard;
export const personAddSVGSource = personAdd;
export const phoneCallSVGSource = phoneCall;
export const shareSVGSource = share;
export const arrowUpSVGSource = arrowUp;
export const arrowUpDoubleSVGSource = arrowUpDouble;
export const checkSVGSource = check;
export const cancelSVGSource = cancel;
export const exclamationSVGSource = exclamation;
export const lockSVGSource = lock;
export const rssSVGSource = rss;
export const smartphoneSVGSource = smartphone;
export const qrcodeSVGSource = qrcode;
export const callGraySVGSource = call_gray;
export const smartphoneGraySVGSource = smartphone_gray;
export const mailGraySVGSource = mail_gray;
export const locationGraySVGSource = location_gray;

interface SVGProps extends ImageProps {
  size?: number;
  color?: string;
}

export function SVG({ src, color, size = 16, ...props }: SVGProps) {
  return (
    <Image
      src={src}
      svg
      style={{
        width: size,
        height: size,
      }}
      {...props}
    />
  );
}
