# JCEC 电子名片字体使用指南

## 概述

本项目已配置了优化的中英文字体栈，基于思源黑体和Helvetica字体系列，使用Tailwind CSS v4的`@theme`指令进行配置。

## 字体配置

### 主要字体变量

| 变量名 | 用途 | 字体栈 |
|--------|------|--------|
| `--font-sans` | 默认字体，中英文混排 | Source Han Sans SC, Helvetica Neue, Arial... |
| `--font-chinese` | 纯中文字体 | Source Han Sans SC, 思源黑体, Microsoft YaHei... |
| `--font-english` | 纯英文字体 | Helvetica Neue, Helvetica, Arial... |
| `--font-heading` | 标题字体 | 与sans相同，但优化了字重和字间距 |
| `--font-numeric` | 数字字体 | 优化数字显示的字体栈 |
| `--font-mono` | 等宽字体 | Source Code Pro, SF Mono, Monaco... |
| `--font-serif` | 印刷字体 | Source Han Serif SC, 思源宋体, Times New Roman... |

## Tailwind CSS 类使用

### 基础字体类

```jsx
// 默认字体 - 推荐用于大部分文本
<Text className="font-sans">JCEC 电子名片 Digital Card</Text>

// 标题字体 - 用于标题和重要文本
<Text className="font-heading">欢迎使用电子名片</Text>

// 纯中文字体 - 当需要强调中文显示效果时
<Text className="font-chinese-only">思源黑体优化显示</Text>

// 纯英文字体 - 当需要强调英文显示效果时  
<Text className="font-english-only">Helvetica for English</Text>

// 数字字体 - 用于电话号码、金额等数字内容
<Text className="font-numeric">138-0013-8000</Text>

// 等宽字体 - 用于代码、固定格式文本
<Text className="font-mono">const name = "JCEC";</Text>
```

### 字体权重类

```jsx
<Text className="font-light">轻体文本 (300)</Text>
<Text className="font-normal">常规文本 (400)</Text>
<Text className="font-medium">中等文本 (500)</Text>
<Text className="font-semibold">半粗文本 (600)</Text>
<Text className="font-bold">粗体文本 (700)</Text>
```

### 文本渲染优化类

```jsx
// 清晰渲染 - 适用于小字号文本
<Text className="text-crisp">小字号清晰显示</Text>

// 平滑渲染 - 适用于大字号文本
<Text className="text-smooth">大字号平滑显示</Text>
```

## 使用建议

### 1. 默认使用场景

- **一般文本内容**: 使用 `font-sans`
- **页面标题**: 使用 `font-heading`
- **数字内容**: 使用 `font-numeric`

### 2. 特殊场景

- **纯中文内容**: 使用 `font-chinese-only`
- **纯英文内容**: 使用 `font-english-only`
- **代码片段**: 使用 `font-mono`

### 3. 字体权重选择

- **正文**: `font-normal` (400)
- **次要信息**: `font-light` (300)
- **重要信息**: `font-medium` (500)
- **标题**: `font-semibold` (600) 或 `font-bold` (700)

### 4. 文本渲染优化

- **12px以下**: 使用 `text-crisp`
- **16px以上**: 使用 `text-smooth`

## 示例组件

项目中包含了 `FontExample` 组件，展示了所有字体配置的使用效果：

```jsx
import { FontExample } from '@/components';

// 在页面中使用
<FontExample />
```

## 字体降级策略

配置的字体栈提供了完整的降级方案：

1. **首选**: 思源黑体 (Source Han Sans SC)
2. **备选**: 系统中文字体 (Microsoft YaHei, PingFang SC等)
3. **最终**: 系统默认字体 (sans-serif)

这确保了在任何设备上都能获得良好的字体显示效果。

## 性能考虑

- 使用CSS字体栈，无需加载外部字体文件
- 优先使用系统预装字体，加载速度快
- 适合小程序环境的性能要求

## 注意事项

1. 在小程序中测试字体效果时，注意不同平台的字体支持差异
2. 思源黑体在较新的设备上支持较好，旧设备会自动降级
3. 数字字体使用了 `font-variant-numeric: tabular-nums` 确保数字对齐
4. 标题字体设置了 `letter-spacing: -0.025em` 优化字间距

## 更新记录

- 2024-01-XX: 初始配置，基于Tailwind CSS v4
- 配置了完整的中英文字体栈
- 添加了字体渲染优化
- 创建了示例组件和使用指南
