import type { BaseEventOrig, ButtonProps } from '@tarojs/components';
import { useWechatLogin } from '../hooks';
import { Button } from './Button';

export default function LoginWithPhoneButton({
  className,
  buttonProps,
  onLogin,
  onCancel,
  onFailed,
  children,
}: {
  className?: string;
  buttonProps?: Omit<ButtonProps, 'className'>;
  onLogin: (success: boolean) => void;
  onCancel: () => void;
  onFailed: (error: string) => void;
  children: React.ReactNode;
}) {
  const { login } = useWechatLogin();
  const onGetPhoneNumber = (e: BaseEventOrig<ButtonProps.onGetPhoneNumberEventDetail>) => {
    const { code, errMsg } = e.detail;
    console.log(e.detail);

    if (!code) {
      if (errMsg.includes('deny')) {
        onCancel();
      } else {
        onFailed(errMsg);
      }
      return;
    }
    login(code)
      .then((res) => {
        if (res) {
          onLogin(true);
        } else {
          onFailed('登录失败');
        }
      })
      .catch((err) => {
        onFailed(err.message);
      });
  };

  return (
    <Button
      className={className}
      openType="getPhoneNumber"
      onGetPhoneNumber={onGetPhoneNumber}
      {...buttonProps}
    >
      {children}
    </Button>
  );
}
