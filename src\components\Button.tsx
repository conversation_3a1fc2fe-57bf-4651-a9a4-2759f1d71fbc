import { Button as TaroButton, type ButtonProps as TaroButtonProps } from '@tarojs/components';
import { cn } from '../utils/merge';

export function Button({
  children,
  onClick,
  className,
  type = 'default',
  ...props
}: TaroButtonProps) {
  // 根据type设置样式
  const buttonStyle = type === 'primary' ? 'bg-blue-500 text-white' : 'bg-white text-black';
  const buttonClassName = cn('rounded-lg px-4 shadow-sm', buttonStyle, className);

  return (
    <TaroButton className={buttonClassName} onClick={onClick} type={type} {...props}>
      {children}
    </TaroButton>
  );
}
