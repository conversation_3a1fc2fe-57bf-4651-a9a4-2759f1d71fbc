/*  #ifdef  H5  */
@import 'tailwindcss';
/*  #endif  */
/*  #ifndef  H5  */
@import 'weapp-tailwindcss';
/*  #endif  */

/* Tailwind v4 字体主题配置 */
@theme {
  /* 中文字体配置 - 思源黑体系列 */
  --font-chinese:
    "Source Han Sans SC",
    "Source Han Sans CN",
    "Noto Sans SC",
    "Noto Sans CJK SC",
    "思源黑体",
    "思源黑体 SC",
    "Microsoft YaHei",
    "微软雅黑",
    "PingFang SC",
    "Hiragino Sans GB",
    "Heiti SC",
    "黑体-简",
    "SimHei",
    "黑体",
    sans-serif;

  /* 英文字体配置 - Helvetica系列 */
  --font-english:
    "Helvetica Neue",
    "Helvetica",
    "Arial",
    "Roboto",
    "Segoe UI",
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    sans-serif;

  /* 混合字体配置 - 中英文混排优化 */
  --font-sans:
    "Source Han Sans SC",
    "Source Han Sans CN",
    "Noto Sans SC",
    "思源黑体",
    "Helvetica Neue",
    "Helvetica",
    "Arial",
    "Microsoft YaHei",
    "微软雅黑",
    "PingFang SC",
    "Hiragino Sans GB",
    "Roboto",
    "Segoe UI",
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    sans-serif;

  /* 等宽字体配置 */
  --font-mono:
    "Source Code Pro",
    "SF Mono",
    "Monaco",
    "Inconsolata",
    "Fira Code",
    "Droid Sans Mono",
    "Courier New",
    monospace;

  /* 印刷字体配置 - 用于标题等 */
  --font-serif:
    "Source Han Serif SC",
    "Source Han Serif CN",
    "Noto Serif SC",
    "思源宋体",
    "Times New Roman",
    "Georgia",
    "serif";

  /* 标题字体配置 - 加粗版本 */
  --font-heading:
    "Source Han Sans SC",
    "Source Han Sans CN",
    "Noto Sans SC",
    "思源黑体",
    "Helvetica Neue",
    "Helvetica",
    "Arial",
    "Microsoft YaHei",
    "微软雅黑",
    "PingFang SC",
    "Hiragino Sans GB",
    sans-serif;

  /* 数字字体配置 - 优化数字显示 */
  --font-numeric:
    "Helvetica Neue",
    "Helvetica",
    "Arial",
    "Source Han Sans SC",
    "思源黑体",
    "Microsoft YaHei",
    "微软雅黑",
    sans-serif;
}

:root {
  /* 使用新的字体配置 */
  font-family: var(--font-sans);
  --primary-color: #007bff;
  --secondary-color: #6c757d;
  box-sizing: border-box;

  /* 字体渲染优化 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* 字体工具类 - 针对特定场景优化 */
.font-chinese-only {
  font-family: var(--font-chinese);
}

.font-english-only {
  font-family: var(--font-english);
}

.font-heading {
  font-family: var(--font-heading);
  font-weight: 600;
  letter-spacing: -0.025em;
}

.font-numeric {
  font-family: var(--font-numeric);
  font-variant-numeric: tabular-nums;
}

/* 字体权重优化 */
.font-light {
  font-weight: 300;
}

.font-normal {
  font-weight: 400;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

/* 文本渲染优化 */
.text-crisp {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

.text-smooth {
  -webkit-font-smoothing: subpixel-antialiased;
  -moz-osx-font-smoothing: auto;
}
