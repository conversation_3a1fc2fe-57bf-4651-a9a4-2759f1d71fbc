import md5 from 'md5';
import Taro from '@tarojs/taro';
import { getTranslateConfig, LANGUAGE_MAP } from '../config/translate';

/**
 * 生成随机数
 */
function generateSalt(): string {
  return Math.random().toString(36).substring(2, 15);
}

/**
 * 生成百度翻译API签名
 * @param query 待翻译文本
 * @param salt 随机数
 * @returns 签名字符串
 */
function generateSign(query: string, salt: string): string {
  const { appid, appkey } = getTranslateConfig();
  const signStr = appid + query + salt + appkey;
  return md5(signStr);
}

/**
 * 调用百度翻译API
 * @param text 待翻译文本
 * @param from 源语言
 * @param to 目标语言
 * @returns 翻译结果
 */
export async function translateWithBaidu(
  text: string,
  from: 'zh' | 'en' | 'ru' = 'zh',
  to: 'zh' | 'en' | 'ru' = 'en'
): Promise<string> {
  try {
    // 如果源语言和目标语言相同，直接返回原文
    if (from === to) {
      return text;
    }

    // 如果文本为空，返回空字符串
    if (!text || text.trim() === '') {
      return '';
    }

    const salt = generateSalt();
    const sign = generateSign(text, salt);
    const config = getTranslateConfig();

    const params = {
      q: text,
      from: LANGUAGE_MAP[from],
      to: LANGUAGE_MAP[to],
      appid: config.appid,
      salt,
      sign,
    };

    console.log('🌍 Translating:', { text, from, to, params });

    const response = await Taro.request({
      url: config.apiUrl,
      method: 'GET',
      data: params,
    });

    console.log('🌍 Translation response:', response);

    if (response.statusCode === 200 && response.data) {
      const result = response.data;

      // 检查是否有错误
      if (result.error_code) {
        console.error('百度翻译API错误:', result.error_code, result.error_msg);
        throw new Error(`Translation API error: ${result.error_code} - ${result.error_msg}`);
      }

      // 提取翻译结果
      if (result.trans_result && result.trans_result.length > 0) {
        return result.trans_result[0].dst;
      } else {
        throw new Error('No translation result found');
      }
    } else {
      throw new Error(`HTTP error: ${response.statusCode}`);
    }
  } catch (error) {
    console.error('翻译失败:', error);
    // 翻译失败时返回原文
    return text;
  }
}

/**
 * 批量翻译文本数组
 * @param texts 待翻译文本数组
 * @param from 源语言
 * @param to 目标语言
 * @returns 翻译结果数组
 */
export async function batchTranslate(
  texts: string[],
  from: 'zh' | 'en' | 'ru' = 'zh',
  to: 'zh' | 'en' | 'ru' = 'en'
): Promise<string[]> {
  try {
    // 过滤空文本
    const validTexts = texts.filter(text => text && text.trim() !== '');
    
    if (validTexts.length === 0) {
      return texts;
    }

    // 将多个文本合并为一个，用特殊分隔符分隔
    const separator = '|||SEPARATOR|||';
    const combinedText = validTexts.join(separator);

    // 翻译合并后的文本
    const translatedCombined = await translateWithBaidu(combinedText, from, to);

    // 分割翻译结果
    const translatedTexts = translatedCombined.split(separator);

    // 确保结果数组长度与输入一致
    const result: string[] = [];
    let translatedIndex = 0;

    for (const originalText of texts) {
      if (originalText && originalText.trim() !== '') {
        result.push(translatedTexts[translatedIndex] || originalText);
        translatedIndex++;
      } else {
        result.push(originalText);
      }
    }

    return result;
  } catch (error) {
    console.error('批量翻译失败:', error);
    // 翻译失败时返回原文数组
    return texts;
  }
}

/**
 * 翻译对象中的文本字段
 * @param obj 包含文本字段的对象
 * @param fields 需要翻译的字段名数组
 * @param from 源语言
 * @param to 目标语言
 * @returns 翻译后的对象
 */
export async function translateObject<T extends Record<string, any>>(
  obj: T,
  fields: (keyof T)[],
  from: 'zh' | 'en' | 'ru' = 'zh',
  to: 'zh' | 'en' | 'ru' = 'en'
): Promise<T> {
  try {
    // 提取需要翻译的文本
    const textsToTranslate = fields.map(field => obj[field] || '');

    // 批量翻译
    const translatedTexts = await batchTranslate(textsToTranslate, from, to);

    // 创建翻译后的对象
    const translatedObj = { ...obj };
    fields.forEach((field, index) => {
      if (translatedTexts[index]) {
        (translatedObj as any)[field] = translatedTexts[index];
      }
    });

    return translatedObj;
  } catch (error) {
    console.error('对象翻译失败:', error);
    return obj;
  }
}
