import { Image, ScrollView, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useEffect, useMemo, useState } from 'react';

import type { CardStyleProps } from '..';
import { addToContacts, makePhoneCall, shareCard } from '../../../../ability';
import { MaterialCard } from '../../../../components';
import { useI18n } from '../../../../i18n/context';
import { translateObject } from '../../../../api/translate';

import { BottomCard } from '../../../../components/Intro/bottomCard';
import { ActionButton } from './ActionButton';
import { InfoRow } from './InfoRow';
import { ScrollToTop } from './ScrollToTop';

import background from '../../../../assets/background2.png';
import { SVG } from '../../../../assets/icon';
import arrowUpDouble from '../../../../assets/icon/arrowUpDouble.svg';
import logo from '../../../../assets/logo/logo_cn_with_en_horizental.svg';
import defaultAvatar from '../../../../assets/placeholder_avatar.jpg';
import { useWechatLogin } from '../../../../hooks';

export default function CardPage({ data }: CardStyleProps) {
  const { isLoggedIn } = useWechatLogin();
  const { t, language, translateText } = useI18n();
  const [showScrollToTop, setShowScrollToTop] = useState(false);
  const [imageError, setImageError] = useState<{ error: boolean; message: string }>({
    error: false,
    message: '',
  });
  const [translatedData, setTranslatedData] = useState(data);

  useEffect(() => {
    Taro.setNavigationBarColor({
      frontColor: '#000000',
      backgroundColor: '#ffffff',
    });
  }, []);

  // 翻译数据
  useEffect(() => {
    const translateCardData = async () => {
      if (language === 'zh') {
        setTranslatedData(data);
        return;
      }

      try {
        const fieldsToTranslate = ['name', 'position', 'title', 'department', 'company', 'address'] as (keyof typeof data)[];
        const translated = await translateObject(data, fieldsToTranslate, 'zh', language);
        setTranslatedData(translated);
      } catch (error) {
        console.error('Translation failed:', error);
        setTranslatedData(data);
      }
    };

    translateCardData();
  }, [data, language]);

  const lastItem = useMemo(() => {
    const items = ['mobile', 'telephone', 'email', 'address'];
    for (const item of items) {
      if (!data[item]) items.splice(items.indexOf(item), 1);
    }
    return items[items.length - 1];
  }, [data.mobile, data.telephone, data.email, data.address]);

  const showCallButton = !!data.mobile || !!data.telephone;
  const handleCall = () => {
    if (!data.mobile && !data.telephone) {
      Taro.showToast({
        title: '没有联系方式',
        icon: 'none',
      });
      return;
    }
    if (!!data.mobile && !!data.telephone) {
      Taro.showActionSheet({
        itemList: [`${t.call}${t.mobile}`, `${t.call}${t.telephone}`],
        success: (res) => {
          // biome-ignore lint/style/noNonNullAssertion: <explanation>
          makePhoneCall(res.tapIndex === 0 ? data.mobile! : data.telephone!);
        },
      });
      return;
    }
    if (data.mobile) makePhoneCall(data.mobile);
    if (data.telephone) makePhoneCall(data.telephone);
  };

  const handleClickMyCard = () => {
    if (isLoggedIn) {
      Taro.navigateTo({
        url: '/pages/card/manage',
      });
    } else {
      Taro.navigateTo({
        url: '/pages/index/index',
      });
    }
  };

  return (
    <ScrollView
      id="scrollview"
      className="flex w-full flex-col justify-center"
      scrollY
      scrollWithAnimation
      upperThreshold={100}
      onScrollToUpper={() => {
        console.log('scroll to top');
        setShowScrollToTop(true);
      }}
      enableFlex
      enableBackToTop
    >
      <View
        className="flex h-[85vh] flex-col px-4"
        style={{
          backgroundImage: `url(${background})`,
          backgroundSize: 'contain',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center 40%',
        }}
      >
        <View className="flex flex-1 items-center justify-center">
          <MaterialCard className="z-10 mt-2 w-full">
            <View className="flex flex-col items-center p-4">
              {/* 公司标识 */}
              <View className="mb-4 text-center">
                <Image src={logo} className="h-10 w-32" mode="aspectFit" />
              </View>

              {/* 头像和基本信息 */}
              <View className="flex w-full">
                {/* 始终显示头像，但根据配置使用实际头像或默认头像 */}
                <View className="relative h-36 w-27">
                  <Image
                    src={data.avatar || defaultAvatar}
                    className="h-full w-full rounded-md"
                    mode="aspectFill"
                    webp={true}
                    lazyLoad={false}
                    onError={(e) => {
                      console.error('头像加载错误:', e.detail);
                      // 记录错误信息
                      const errMsg = e.detail?.errMsg || '未知错误';
                      setImageError({
                        error: true,
                        message: `加载失败: ${errMsg}, 图片路径: ${data.avatar || '使用默认头像'}`,
                      });

                      // 显示错误提示
                      Taro.showToast({
                        title: '头像加载失败',
                        icon: 'none',
                        duration: 2000,
                      });
                    }}
                    onLoad={(e) => {
                      console.log('头像加载成功:', e.detail);
                      // 清除错误状态
                      setImageError({ error: false, message: '' });
                    }}
                    style={{
                      // 添加一些基础样式确保图片可见
                      backgroundColor: '#f0f0f0', // 添加背景色以便于调试
                    }}
                  />
                  {imageError.error && (
                    <View className="bg-opacity-70 absolute right-0 bottom-0 left-0 bg-red-500 p-1 text-center">
                      <Text className="text-xs text-white">{t.imageLoadFailed}</Text>
                    </View>
                  )}
                </View>
                <View className="ml-4 flex flex-1 flex-col justify-end">
                  {/* 始终显示名称，但根据配置使用实际名称或"联系人" */}
                  <Text className="text-2xl font-bold">{translatedData.name ?? t.contactPerson}</Text>
                  {translatedData.position && (
                    <Text className="text-base text-gray-600">{translatedData.position}</Text>
                  )}
                  {translatedData.title && <Text className="text-base text-gray-600">{translatedData.title}</Text>}
                  {translatedData.department && (
                    <Text className="text-base text-gray-600">{translatedData.department}</Text>
                  )}
                </View>
              </View>

              {/* 公司名称 */}
              <View className="mt-8 w-full">
                {translatedData.company && <Text className="px-2 text-base font-bold">{translatedData.company}</Text>}
              </View>

              {/* 联系方式 */}
              <View className="mt-2 w-full">
                {data.mobile && (
                  <InfoRow
                    label={t.mobile}
                    value={data.mobile}
                    border={lastItem === 'mobile' ? 'none' : 'bottom'}
                  />
                )}
                {data.telephone && (
                  <InfoRow
                    label={t.telephone}
                    value={data.telephone}
                    border={lastItem === 'telephone' ? 'none' : 'bottom'}
                  />
                )}
                {data.email && (
                  <InfoRow
                    label={t.email}
                    value={data.email}
                    border={lastItem === 'email' ? 'none' : 'bottom'}
                  />
                )}
                {translatedData.address && <InfoRow label={t.officeAddress} value={translatedData.address} border="none" />}
              </View>
            </View>
          </MaterialCard>
        </View>

        {/* 底部按钮 */}

        <View className="z-10 mt-6 grid w-full grid-cols-2 gap-3">
          {showCallButton && <ActionButton type="call" onClick={handleCall} />}
          <ActionButton
            className={!showCallButton ? 'col-span-2' : ''}
            type="addContact"
            onClick={() => addToContacts(data)}
          />
          <ActionButton type="share" onClick={() => shareCard(data)} />
          <ActionButton type="myCard" onClick={handleClickMyCard} />
        </View>
      </View>

      <View className="z-10 flex h-10 w-full translate-y-2 items-center justify-center">
        <SVG src={arrowUpDouble} className="h-10 w-10" />
      </View>

      <BottomCard />

      {showScrollToTop && <ScrollToTop />}
    </ScrollView>
  );
}
