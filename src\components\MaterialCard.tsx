import { View, ViewProps } from '@tarojs/components';

interface CardProps extends ViewProps {
  className?: string;
  children: React.ReactNode;
}

export const MaterialCard = ({ className, children, ...props }: CardProps) => {
  return (
    <View
      className={`rounded-2xl border-1 border-[#e0e0e0af] bg-[#ffffffdc] shadow-xl ${className}`}
      {...props}
    >
      {children}
    </View>
  );
};
