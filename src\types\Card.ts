/**
 * 名片相关类型定义
 */
export interface CardInfo {
  id: string; // 名片ID
  userId: string; // 用户ID

  // 基本信息
  avatar: string; // 头像 url
  name: string; // 姓名
  title: string; // 职位
  position: string; // 职务
  company: string; // 公司名称
  department: string; // 部门

  // 联系方式
  mobile: string; // 手机号
  telephone?: string; // 座机号（可选）
  email: string; // 邮箱
  address: string; // 办公地址
}

export interface CardConfig {
  id: string;
  cardId: string;

  name: string;
  styleName: string;

  showAvatar: boolean;
  showName: boolean;
  showDepartment: boolean;
  showPosition: boolean;
  showTitle: boolean;
  showCompany: boolean; // 公司名称必须展示
  showMobile: boolean;
  showTelephone: boolean;
  showEmail: boolean;
  showAddress: boolean;

  public: boolean;
}
