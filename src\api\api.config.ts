/**
 * API配置文件
 * 包含API相关的常量和配置
 */

const env = {
  NODE_ENV: process.env.NODE_ENV,
  TARO_APP_API_BASE_URL: process.env.TARO_APP_API_BASE_URL,
  TARO_APP_SM4_KEY: process.env.TARO_APP_SM4_KEY,
};

// 从环境变量获取配置
const getEnv = (key: string, defaultValue = ''): string => {
  // 尝试从Taro环境变量获取
  const value = env[key];
  return value || defaultValue;
};

// API基础URL
export const BASE_URL = getEnv('TARO_APP_API_BASE_URL', 'http://localhost:3000');

// SM4加密密钥 (与服务端保持一致)
export const SM4_KEY = getEnv('TARO_APP_SM4_KEY', 'dianzimingpianoa');

// API路径
export const API_PATHS = {
  // 微信相关
  WECHAT: {
    // 微信登录
    LOGIN: '/api/wechat/login',
  },
  // 名片相关
  CARD: {
    // 获取名片信息
    INFO: '/api/wechat/card/info',
    // 获取/创建名片配置
    CONFIGS: '/api/wechat/card/configs',
    // 更新/删除名片配置
    UPDATE_CONFIG: (id: string) => `/api/wechat/card/configs/${id}`,
    // 获取公开名片信息
    PUBLIC_INFO: (id: string) => `/api/wechat/public/${id}`,
  },
  INFO: {
    // 获取info
    GET: (name: string) => `/api/wechat/info/${name}`,
  },
};

// HTTP状态码
export const HTTP_STATUS = {
  SUCCESS: 200,
  CREATED: 201,
  ACCEPTED: 202,
  CLIENT_ERROR: 400,
  AUTHENTICATE: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
};
