import { ScrollView, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import React, { useEffect, useState } from 'react';
import { Button, MaterialCard, PageView } from '../../components';
import { useCard } from '../../hooks';
import type { CardConfig } from '../../types';

import {
  SVG,
  cancelSVGSource,
  checkSVGSource,
  exclamationSVGSource,
  lockSVGSource,
  rssSVGSource,
} from '../../assets/icon';

const CardInfoCell_impl = ({ label, show }: { label: string; show: boolean | 'other' }) => {
  let icon: string;
  let color: string;
  switch (show) {
    case true:
      icon = checkSVGSource;
      color = '#55B837';
      break;
    case false:
      icon = cancelSVGSource;
      color = '#666666';
      break;
    case 'other':
      icon = exclamationSVGSource;
      color = '#666666';
      break;
    default:
      icon = exclamationSVGSource;
      color = '#666666';
  }
  return (
    <View className="flex flex-row items-center justify-center">
      <SVG src={icon} size={16} className="mr-1" />
      <Text className="mr-2" style={{ color }}>
        {label}
      </Text>
    </View>
  );
};

const CardInfoCell = React.memo(CardInfoCell_impl);
CardInfoCell.displayName = 'CardInfoCell';

export default function CardManageConfigPage() {
  // 获取名片配置数据
  const { cardConfigs, deleteCardConfig } = useCard();
  const [editMode, setEditMode] = useState(false);

  const handleCreateConfig = () => {
    Taro.navigateTo({
      url: '/pages/card/config',
    });
  };

  const handlePreview = (config: CardConfig) => {
    Taro.navigateTo({
      url: `/pages/card/index?id=${config.id}`,
    });
  };

  const handleDelete = async (config: CardConfig) => {
    Taro.showModal({
      title: '确认删除',
      content: '删除此配置后，已经分享出去的名片将无法被查看，确定要删除吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            await deleteCardConfig(config.id);

            Taro.showToast({
              title: '删除成功',
              icon: 'success',
            });
          } catch (error) {
            console.error('删除配置失败:', error);

            // 根据错误类型显示不同的提示
            if (error instanceof Error) {
              if (error.message.includes('未找到名片配置')) {
                Taro.showToast({
                  title: '名片配置不存在或已被删除',
                  icon: 'none',
                });
              } else if (error.message.includes('无权操作')) {
                Taro.showToast({
                  title: '无权操作此名片配置',
                  icon: 'none',
                });
              } else if (error.message.includes('未登录') || error.message.includes('401')) {
                Taro.showToast({
                  title: '登录已过期，请重新登录',
                  icon: 'none',
                });
                // 跳转到登录页
                setTimeout(() => {
                  Taro.navigateTo({
                    url: '/pages/login/index',
                  });
                }, 1500);
              } else {
                Taro.showToast({
                  title: '删除失败',
                  icon: 'error',
                });
              }
            } else {
              Taro.showToast({
                title: '删除失败',
                icon: 'error',
              });
            }
          }
        }
      },
    });
  };

  const handleEdit = (config: CardConfig) => {
    Taro.navigateTo({
      url: `/pages/card/config?id=${config.id}`,
    });
  };

  const toggleEditMode = () => {
    setEditMode(!editMode);
  };

  // 当没有名片配置时，自动关闭编辑模式
  useEffect(() => {
    if (cardConfigs?.length === 0) {
      setEditMode(false);
    }
  }, [cardConfigs]);

  const renderConfigItem = (config: CardConfig, index: number) => {

    return (
      <MaterialCard
        key={index}
        className="mb-4 p-4"
        onClick={() => !editMode && handlePreview(config)}
      >
        <View className="flex justify-between">
          <View className="flex flex-row items-center gap-2">
            <Text className="text-lg font-bold">{config.name}</Text>
            <View className="flex flex-row items-center gap-1 rounded-lg bg-gray-100 px-2 py-1">
              <SVG src={config.public ? rssSVGSource : lockSVGSource} size={16} />
              <Text className="text-sm text-gray-500">{config.public ? '公开' : '未公开'}</Text>
            </View>
          </View>
          {editMode && (
            <View className="flex flex-row gap-2">
              <View
                className="ml-2 flex items-center justify-center rounded bg-blue-500 px-4 text-white"
                onClick={(e) => {
                  e.stopPropagation();
                  handleEdit(config);
                }}
              >
                <Text>编辑</Text>
              </View>
              <View
                className="ml-2 flex items-center justify-center rounded bg-red-500 px-4 text-white"
                onClick={(e) => {
                  e.stopPropagation();
                  handleDelete(config);
                }}
              >
                <Text>删除</Text>
              </View>
            </View>
          )}
        </View>

        <View className="mt-2 flex flex-row flex-wrap gap-0.5">
          <CardInfoCell label="头像" show={config.showAvatar} />
          <CardInfoCell label="姓名" show={config.showName} />
          <CardInfoCell label="职位" show={config.showTitle} />
          <CardInfoCell label="职务" show={config.showPosition} />
          <CardInfoCell label="公司" show={config.showCompany} />
          <CardInfoCell label="部门" show={config.showDepartment} />
          <CardInfoCell label="手机" show={config.showMobile} />
          <CardInfoCell label="电话" show={config.showTelephone} />
          <CardInfoCell label="邮箱" show={config.showEmail} />
          <CardInfoCell label="地址" show={config.showAddress} />
        </View>
      </MaterialCard>
    );
  };

  const hasCardConfig = cardConfigs && cardConfigs.length > 0;

  const renderMainContent = () => {
    return (
      <PageView className={hasCardConfig ? 'items-start' : ''}>
        {hasCardConfig ? (
          <ScrollView className="w-full p-4 pb-24" scrollY>
            {cardConfigs.map((config, index) => renderConfigItem(config, index))}
          </ScrollView>
        ) : (
          <Text className="text-gray-500">暂无名片配置，请创建新配置</Text>
        )}

        {/* 底部按钮 */}
        <View className="fixed bottom-0 mt-4 w-full border-t border-gray-200 bg-white pt-2 pb-8 shadow-md">
          <View className="flex flex-row flex-nowrap justify-between px-4">
            {hasCardConfig && (
              <Button
                className={`flex-1 rounded-lg px-4 ${editMode && 'bg-green-500 text-white'}`}
                onClick={toggleEditMode}
              >
                {editMode ? '完成' : '编辑'}
              </Button>
            )}
            <Button
              className="ml-2 flex-3 rounded-lg px-4"
              type="primary"
              onClick={handleCreateConfig}
            >
              创建新配置
            </Button>
          </View>
        </View>
      </PageView>
    );
  };

  return renderMainContent();
}
