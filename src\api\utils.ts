import Taro from '@tarojs/taro';
import { globalStore } from '../store';
import { tokenAtom } from '../store/token';

/**
 * 导航栏加载高阶函数
 * 在异步函数执行期间显示导航栏加载指示器
 * @param fn 异步函数
 */
export function withNavigationLoadingHOF<T extends (...args: unknown[]) => Promise<unknown>>(
  fn: T,
): T {
  return (async (...args: unknown[]) => {
    Taro.showNavigationBarLoading();
    try {
      const result = await fn(...args);
      return result;
    } finally {
      Taro.hideNavigationBarLoading();
    }
  }) as T;
}

/**
 * 处理API响应状态码
 * @param statusCode HTTP状态码
 * @param customMessage 自定义错误消息
 */
export function handleApiStatusCode(statusCode: number, customMessage?: string): void {
  // 处理401未授权错误
  if (statusCode === 401) {
    // 清除token (使用全局状态管理)
    globalStore.set(tokenAtom, '');
    throw new Error(customMessage || '登录已过期，请重新登录');
  }

  // 处理404错误
  if (statusCode === 404) {
    throw new Error(customMessage || '请求的资源不存在');
  }

  // 处理400错误（包括名称重复等验证错误）
  if (statusCode === 400) {
    throw new Error(customMessage || '请求参数错误');
  }

  // 处理其他错误
  throw new Error(customMessage || `请求失败，状态码: ${statusCode}`);
}
