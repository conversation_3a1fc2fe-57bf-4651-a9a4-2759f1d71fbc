/**
 * 名片配置相关API
 */
import Taro from '@tarojs/taro';
import type { ApiResponse, CardConfig } from '../types';
import { Api } from './api-crypto';
import { API_PATHS, BASE_URL } from './api.config';
import { withNavigationLoadingHOF } from './utils';

// API URL
const cardConfigsUrl = BASE_URL + API_PATHS.CARD.CONFIGS;

/**
 * 获取名片配置列表
 * @returns 名片配置列表
 */
export const getCardConfigs = withNavigationLoadingHOF(
  async (token: string): Promise<CardConfig[]> => {
    const api = new Api();
    try {
      const response = await api.get<ApiResponse<CardConfig[]>>(cardConfigsUrl, token);

      if (!response?.data) {
        throw new Error(response?.msg || '获取名片配置失败');
      }

      return response.data;
    } catch (error) {
      console.error('获取名片配置失败:', error);
      Taro.showToast({
        title: '获取名片配置失败',
        icon: 'error',
      });
      throw error;
    }
  },
);

/**
 * 创建新的名片配置
 * @param config 名片配置数据
 * @returns 创建的名片配置
 */
export const createCardConfig = withNavigationLoadingHOF(
  async (config: Partial<CardConfig>, token: string): Promise<CardConfig> => {
    const api = new Api();
    try {
      const response = await api.post<ApiResponse<CardConfig>, Partial<CardConfig>>(
        cardConfigsUrl,
        config,
        token,
      );

      if (!response?.data) {
        if (response?.msg?.includes('名片配置名称已存在')) {
          throw new Error('名片配置名称已存在');
        }
        throw new Error(response?.msg || '创建名片配置失败');
      }

      return response.data;
    } catch (error) {
      console.error('创建名片配置失败:', error);

      // 直接将错误传递给调用者，由页面组件处理
      throw error;
    }
  },
);

/**
 * 更新名片配置
 * @param id 配置ID
 * @param config 更新的配置数据
 * @returns 更新后的名片配置
 */
export const updateCardConfig = withNavigationLoadingHOF(
  async (id: string, config: Partial<CardConfig>, token: string): Promise<CardConfig> => {
    const api = new Api();
    const url = BASE_URL + API_PATHS.CARD.UPDATE_CONFIG(id);

    try {
      const response = await api.patch<ApiResponse<CardConfig>, Partial<CardConfig>>(
        url,
        config,
        token,
      );

      if (!response?.data) {
        if (response?.msg?.includes('名片配置名称已存在')) {
          throw new Error('名片配置名称已存在');
        }
        throw new Error(response?.msg || '更新名片配置失败');
      }

      return response.data;
    } catch (error) {
      console.error('更新名片配置失败:', error);

      // 直接将错误传递给调用者，由页面组件处理
      throw error;
    }
  },
);

/**
 * 删除名片配置
 * @param id 配置ID
 * @returns 删除结果
 */
export const deleteCardConfig = withNavigationLoadingHOF(
  async (id: string, token: string): Promise<{ success: boolean }> => {
    const api = new Api();
    const url = BASE_URL + API_PATHS.CARD.UPDATE_CONFIG(id);

    try {
      const response = await api.delete<ApiResponse<{ success: boolean }>>(url, token);

      if (!response?.data) {
        throw new Error(response?.msg || '删除名片配置失败');
      }

      return response.data;
    } catch (error) {
      console.error('删除名片配置失败:', error);
      // 直接将错误传递给调用者，由页面组件处理
      throw error;
    }
  },
);
