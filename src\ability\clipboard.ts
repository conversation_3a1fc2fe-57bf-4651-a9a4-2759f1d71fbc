import Taro from '@tarojs/taro';

export const copyToClipboard = (text: string, title: string) => {
  Taro.setClipboardData({
    data: text,
    success: () => {
      Taro.showToast({
        title: `${title}已复制`,
        icon: 'success',
      });
    },
    fail: (err) => {
      console.error('复制失败', err);
      Taro.showToast({
        title: '复制失败',
        icon: 'error',
      });
    },
  });
};
