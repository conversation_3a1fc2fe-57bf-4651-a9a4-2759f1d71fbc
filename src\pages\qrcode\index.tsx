import { Image, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { Button, PageView } from '../../components';

import qrcodeImg from '../../assets/qrcode/jcec.jpg';

export default function QRCodePage() {
  const handleBack = () => {
    Taro.navigateBack();
  };

  return (
    <PageView>
      <View className="flex w-full flex-col items-center justify-center">
        <Text className="mb-6 text-center text-xl font-bold">关注公众号</Text>
        <Image
          src={qrcodeImg}
          className="h-64 w-64 rounded-lg"
          mode="aspectFit"
          showMenuByLongpress
        />
        <Text className="mt-6 text-center text-base text-gray-600">
          长按上方二维码，识别并关注公众号
        </Text>
        <Button onClick={handleBack} type="primary" className="mt-10">
          返回
        </Button>
      </View>
    </PageView>
  );
}
