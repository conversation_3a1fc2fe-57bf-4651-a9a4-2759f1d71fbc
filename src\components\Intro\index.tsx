import { Image, RichText, Text, Video, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { BASE_URL } from '../../api/api.config';
import { SVG } from '../../assets/icon';
import qrcodeSVGSource from '../../assets/icon/qrcode.svg';
import { useInfo } from '../../hooks';
import { Button } from '../Button';
import { introInfo } from './info';

const companyImg = `${BASE_URL}/static/company-building-image.jpg`;
const videoUrl = `${BASE_URL}/static/xcsp.mp4`;

export function CompanyIntro() {
  // 使用TanStack Query获取公司介绍信息
  const { data: companyIntroData, isLoading, error } = useInfo('company-intro');

  const navigateToQRCode = () => {
    Taro.navigateTo({
      url: '/pages/qrcode/index',
    });
  };

  // 视频错误处理
  const handleVideoError = (e: any) => {
    console.error('视频播放错误:', e);
    Taro.showToast({
      title: '视频加载失败',
      icon: 'none',
    });
  };

  // 渲染公司介绍内容
  const renderCompanyDescription = () => {
    if (isLoading) {
      return <Text className="text-center text-gray-500">加载中...</Text>;
    }

    if (error) {
      console.error('获取公司介绍失败:', error);
      // 如果获取失败，回退到静态内容
      return (
        <>
          {introInfo.companyDescription.map((description, index) => (
            <Text key={index}>{description}</Text>
          ))}
        </>
      );
    }

    if (companyIntroData && 'content' in companyIntroData) {
      try {
        // 尝试解析meta信息
        const contentType = companyIntroData.meta.type || 'html';

        if (contentType === 'html') {
          // 使用RichText渲染HTML内容
          return <RichText nodes={companyIntroData.content} />;
        } else {
          // 对于纯文本内容，按行分割显示
          return (
            <>
              {companyIntroData.content.split('\n').map((line, index) => (
                <Text key={index} className="mb-2">
                  {line}
                </Text>
              ))}
            </>
          );
        }
      } catch (parseError) {
        console.error('解析meta信息失败:', parseError);
        // 解析失败时按纯文本处理
        return (
          <>
            {companyIntroData.content.split('\n').map((line, index) => (
              <Text key={index} className="mb-2">
                {line}
              </Text>
            ))}
          </>
        );
      }
    }

    // 默认回退到静态内容
    return (
      <>
        {introInfo.companyDescription.map((description, index) => (
          <Text key={index}>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{description}</Text>
        ))}
        <Image
          src={companyImg}
          className="h-[12rem] w-full rounded-lg"
          mode="aspectFill"
          lazyLoad={true}
        />
      </>
    );
  };

  return (
    <View className="flex flex-1 flex-col justify-center gap-2 p-4">
      <View className="flex flex-row items-center justify-center gap-4">
        <Image src={introInfo.companyLogo} className="h-8 w-8" mode="aspectFit" />
        <Text className="text-lg font-bold">{introInfo.companyName}</Text>
      </View>

      {/* 关注公众号按钮 */}
      <Button
        onClick={navigateToQRCode}
        type="default"
        className="my-2 flex flex-row items-center justify-center gap-2 border border-gray-400 bg-green-500"
      >
        <SVG src={qrcodeSVGSource} size={20} />
        <Text className="text-white">关注公众号</Text>
      </Button>

      {/* 添加视频组件 */}
      <Video
        src={videoUrl}
        title="JCEC 介绍视频"
        className="my-2 h-[16rem] w-full rounded-lg shadow-lg"
        direction={90}
        controls={true}
        autoplay={true}
        loop={true}
        muted={true}
        showFullscreenBtn={true}
        showPlayBtn={true}
        showCenterPlayBtn={true}
        showMuteBtn={true}
        enableProgressGesture={false}
        objectFit="cover"
        onError={handleVideoError}
      />

      {renderCompanyDescription()}

      {/* <Text className="text-center text-sm text-gray-500">{introInfo.updateTime}</Text> */}
    </View>
  );
}
