import Taro from "@tarojs/taro";
import { API_PATHS, BASE_URL } from "./api.config";
import { withNavigationLoadingHOF } from "./utils";
import { InfoResponse } from "../types/Info";

export const getInfo = withNavigationLoadingHOF(
  async (name: string): Promise<InfoResponse> => {
    try {
      const response = await Taro.request<InfoResponse>({
        url: BASE_URL + API_PATHS.INFO.GET(name),
        method: 'GET',
      });

      if (!response?.data || "error" in response.data) {
        throw new Error(response?.errMsg || "获取info失败");
      }

      return response.data;
    } catch (error) {
      console.error("获取info失败:", error);
      Taro.showToast({
        title: "获取info失败",
        icon: "error",
      });
      throw error;
    }
  },
);
