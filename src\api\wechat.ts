/**
 * 微信相关API
 */
import type { ApiResponse, SignInWithPhoneBody, SignInWithPhoneResponse } from '../types';
import { Api } from './api-crypto';
import { API_PATHS, BASE_URL } from './api.config';

// 微信登录URL
const loginUrl = BASE_URL + API_PATHS.WECHAT.LOGIN;

/**
 * 使用微信手机号登录
 * @param phoneCode 微信手机号码凭证
 * @returns 登录响应
 */
export async function loginWithWechatPhone(phoneCode: string): Promise<SignInWithPhoneResponse> {
  const api = new Api();
  try {
    const response = await api.post<ApiResponse<SignInWithPhoneResponse>, SignInWithPhoneBody>(
      loginUrl,
      {
        code: phoneCode,
      },
    );
    if (!response.data) {
      throw new Error(response.msg || '微信登录失败');
    }

    return response.data;
  } catch (error) {
    console.error('微信登录失败:', error);
    throw error;
  }
}
