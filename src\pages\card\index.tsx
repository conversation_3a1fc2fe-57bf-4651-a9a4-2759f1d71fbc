import { Image, Text, View } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import { MaterialCard, PageView } from '../../components';
import { BusinessCard } from './styles';

import { useEffect } from 'react';
import logo from '../../assets/logo/logo_cn_with_en_horizental.svg';
import { usePublicCard } from '../../hooks';

export default function CardPage() {
  const router = useRouter();
  const configId = router.params.id ?? '';
  const { data: card, isLoading, error } = usePublicCard(configId);

  console.log('🌏CardPage', { params: router.params, card });

  useEffect(() => {
    if (card?.public) {
      Taro.showShareMenu({
        showShareItems: ['wechatFriends', 'wechatMoment'],
      });
    } else {
      Taro.hideShareMenu();
    }
  }, [card]);

  const renderLoadingPage = () => {
    return (
      <PageView className="flex flex-col items-center justify-center space-y-5">
        {/* Logo */}
        <View className="mb-5 flex flex-col items-center justify-center">
          <Image src={logo} className="w-[60vw]" mode="aspectFit" />
          <Text className="text-md mt-5 font-medium text-gray-400">- 电子名片 -</Text>
        </View>

        {/* 加载指示器 */}
        <View className="flex h-[60vh] items-center justify-center">
          <View
            className="h-10 w-10 animate-spin rounded-full border-8 border-t-8 border-gray-200 border-t-blue-500"
            style={{
              borderRadius: '100%',
            }}
          />
        </View>
      </PageView>
    );
  };

  // 悬浮在页面顶部的未公开提示
  const renderUnPublicWarning = () => {
    return (
      <View className="fixed top-0 z-100 w-full flex flex-col items-center justify-center space-y-5 bg-yellow-200 text-sm text-gray-500">
        <Text>此名片未公开，无法分享</Text>
      </View>
    );
  };

  // 错误提示页面
  const renderErrorPage = () => {
    const errorTitle = '获取名片信息失败';
    const errorMessage = '无法获取名片信息，请联系名片提供者';

    return (
      <PageView className="flex flex-col items-center justify-center space-y-5">
        <MaterialCard className="flex w-[80vw] flex-col items-center justify-center p-6">
          {/* Logo */}
          <Image src={logo} className="h-6" mode="aspectFit" />
          <Text className="text-md mt-5 font-medium text-gray-400">- 电子名片 -</Text>

          <Text className="mt-5 text-xl font-bold">{errorTitle}</Text>
          <Text className="text-center text-gray-500">{errorMessage}</Text>
        </MaterialCard>
      </PageView>
    );
  };

  // 根据状态渲染不同的页面
  if (isLoading) {
    return renderLoadingPage();
  }

  if (error) {
    return renderErrorPage();
  }

  if (!card) {
    return renderErrorPage();
  }

  return (
    <>
      {card.public === false && renderUnPublicWarning()}
      <BusinessCard info={card.info} style={card.style} public={card.public} />
    </>
  );
}
