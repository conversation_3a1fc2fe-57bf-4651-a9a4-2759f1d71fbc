/**
 * 名片数据 Hook
 */
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import placeholderAvatar from '../assets/placeholder_avatar.jpg';

import { useAtomValue } from 'jotai';
import { getCardInfo } from '../api/card';
import {
  createCardConfig,
  deleteCardConfig,
  getCardConfigs,
  updateCardConfig,
} from '../api/cardConfig';
import { tokenAtom } from '../store/token';
import type { CardConfig } from '../types';

/**
 * 名片数据 Hook（用户登录状态）
 */
export function useCard() {
  const queryClient = useQueryClient();
  const token = useAtomValue(tokenAtom);
  const cardInfoQuery = useQuery({
    queryKey: ['cardInfo'],
    queryFn: async () => {
      const cardInfo = await getCardInfo(token);
      if (!cardInfo.avatar) {
        cardInfo.avatar = placeholderAvatar;
      }
      return cardInfo;
    },
    enabled: !!token,
  });
  const cardConfigsQuery = useQuery({
    queryKey: ['cardConfigs'],
    queryFn: () => getCardConfigs(token),
    enabled: !!token,
  });

  const invalidateQueries = () => {
    queryClient.invalidateQueries({ queryKey: ['cardConfigs'] });
    queryClient.invalidateQueries({ queryKey: ['publicCard'] });
  };

  const createCardConfigMutation = useMutation({
    mutationFn: (config: Partial<CardConfig>) => createCardConfig(config, token),
    onSettled: invalidateQueries,
  });
  const updateCardConfigMutation = useMutation({
    mutationFn: ({ id, config }: { id: string; config: Partial<CardConfig> }) =>
      updateCardConfig(id, config, token),
    onSettled: invalidateQueries,
  });
  const deleteCardConfigMutation = useMutation({
    mutationFn: (id: string) => deleteCardConfig(id, token),
    onSettled: invalidateQueries,
  });

  return {
    cardInfo: cardInfoQuery.data,
    cardConfigs: cardConfigsQuery.data,
    createCardConfig: createCardConfigMutation.mutateAsync,
    updateCardConfig: updateCardConfigMutation.mutateAsync,
    deleteCardConfig: deleteCardConfigMutation.mutateAsync,
  };
}
