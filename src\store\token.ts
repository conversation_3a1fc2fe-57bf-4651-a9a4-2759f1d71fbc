import Taro from '@tarojs/taro';
import { atomWithStorage } from 'jotai/utils';

const isDev = process.env.NODE_ENV === 'development';

export const tokenAtom = atomWithStorage<string>('token', '', {
  async getItem(key, initialValue) {
    try {
      const result = await Taro.getStorage({ key });
      if (isDev) {
        console.log('📦getItem', key, result);
      }
      return result.data || initialValue;
    } catch (error) {
      return initialValue;
    }
  },
  async setItem(key, value) {
    if (isDev) {
      console.log('📦setItem', key, value);
    }
    await Taro.setStorage({ key, data: value });
  },
  async removeItem(key) {
    if (isDev) {
      console.log('📦removeItem', key);
    }
    await Taro.removeStorage({ key });
  },
});
