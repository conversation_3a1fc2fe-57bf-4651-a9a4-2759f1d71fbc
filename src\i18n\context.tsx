import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import Taro from '@tarojs/taro';
import { Language, translations, TranslationKeys } from './index';

interface I18nContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: TranslationKeys;
  translateText: (text: string, targetLang?: Language) => Promise<string>;
}

const I18nContext = createContext<I18nContextType | undefined>(undefined);

interface I18nProviderProps {
  children: ReactNode;
}

const STORAGE_KEY = 'app_language';

export function I18nProvider({ children }: I18nProviderProps) {
  const [language, setLanguageState] = useState<Language>('zh');

  // 初始化语言设置
  useEffect(() => {
    const initLanguage = async () => {
      try {
        // 尝试从本地存储获取语言设置
        const savedLanguage = await Taro.getStorage({ key: STORAGE_KEY });
        if (savedLanguage.data && ['zh', 'en', 'ru'].includes(savedLanguage.data)) {
          setLanguageState(savedLanguage.data as Language);
        }
      } catch (error) {
        // 如果没有保存的语言设置，使用默认的中文
        console.log('No saved language, using default zh');
      }
    };

    initLanguage();
  }, []);

  const setLanguage = async (lang: Language) => {
    setLanguageState(lang);
    try {
      await Taro.setStorage({ key: STORAGE_KEY, data: lang });
    } catch (error) {
      console.error('Failed to save language setting:', error);
    }
  };

  // 获取当前语言的翻译
  const t = translations[language];

  // 翻译文本函数（用于动态内容）
  const translateText = async (text: string, targetLang?: Language): Promise<string> => {
    const target = targetLang || language;
    
    // 如果目标语言是中文，直接返回原文
    if (target === 'zh') {
      return text;
    }

    try {
      // 调用百度翻译API
      const response = await Taro.request({
        url: '/api/translate',
        method: 'POST',
        data: {
          text,
          from: 'zh',
          to: target,
        },
      });

      if (response.data && response.data.success && response.data.result) {
        return response.data.result;
      } else {
        console.warn('Translation failed, returning original text');
        return text;
      }
    } catch (error) {
      console.error('Translation error:', error);
      return text;
    }
  };

  const value: I18nContextType = {
    language,
    setLanguage,
    t,
    translateText,
  };

  return <I18nContext.Provider value={value}>{children}</I18nContext.Provider>;
}

export function useI18n(): I18nContextType {
  const context = useContext(I18nContext);
  if (context === undefined) {
    throw new Error('useI18n must be used within an I18nProvider');
  }
  return context;
}

// 便捷的翻译Hook
export function useTranslation() {
  const { t, translateText, language } = useI18n();
  
  return {
    t,
    translateText,
    language,
  };
}
