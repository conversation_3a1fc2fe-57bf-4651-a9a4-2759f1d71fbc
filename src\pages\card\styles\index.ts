import type { PublicCardInfoResponse } from '../../../api/card';
import type { CardInfo } from '../../../types';
import DefaultCardStyle from './default';
import RedCardStyle from './red';

export interface CardStyleProps {
  data: Partial<CardInfo>;
}

// 临时配置：隐藏default主题，所有样式都使用red主题
// TODO: 这是临时修改，需要时可以轻松复原
const TEMP_HIDE_DEFAULT_THEME = true;

const styleMap: Record<string, (props: CardStyleProps) => React.ReactNode> = {
  default: DefaultCardStyle,
  red: RedCardStyle,
};

export function getCardStyle(style: string) {
  // 临时修改：如果启用了隐藏default主题，将所有default样式重定向到red
  if (TEMP_HIDE_DEFAULT_THEME && style === 'default') {
    return styleMap.red;
  }

  const CardStyle = styleMap[style];
  if (!CardStyle) {
    // 临时修改：默认也使用red样式而不是default
    return TEMP_HIDE_DEFAULT_THEME ? styleMap.red : styleMap.default;
  }
  return CardStyle;
}

export function BusinessCard(props: PublicCardInfoResponse) {
  console.log('🌏BusinessCard', props);

  return getCardStyle(props.style)({ data: props.info });
}
