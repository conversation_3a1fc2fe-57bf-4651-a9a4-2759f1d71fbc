import { Button, Checkbox, Input, Radio, Text, View } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import { useEffect, useState } from 'react';
import { PageView } from '../../components';
import { useCard, useWechatLogin } from '../../hooks';
import type { CardConfig } from '../../types';

export default function CardConfigPage() {
  const router = useRouter();
  const configId = router.params.id ?? null;
  const isEditMode = configId !== null;

  const { isLoggedIn } = useWechatLogin();
  const { createCardConfig, updateCardConfig, cardConfigs } = useCard();

  // 临时配置：隐藏default主题，强制使用red样式
  // TODO: 这是临时修改，需要时可以轻松复原
  const TEMP_HIDE_DEFAULT_THEME = true;

  const [configData, setConfigData] = useState<Partial<CardConfig>>({
    name: '',
    styleName: TEMP_HIDE_DEFAULT_THEME ? 'red' : 'default', // 临时修改：默认使用red样式
    showAvatar: true,
    showName: true,
    showTitle: true,
    showPosition: true, // 职务
    showCompany: true, // 公司名称必须展示
    showDepartment: true, // 部门
    showMobile: true,
    showTelephone: true,
    showEmail: true,
    showAddress: true,
    public: false, // 默认不公开
  });

  useEffect(() => {
    if (!isLoggedIn) {
      console.log('🌏CardConfigPage', isLoggedIn);
      Taro.navigateTo({
        url: '/pages/login/index',
      });
    }
  }, [isLoggedIn]);

  // 编辑模式下加载配置数据
  useEffect(() => {
    if (isEditMode && cardConfigs) {
      const config = cardConfigs.find((config) => config.id === configId);
      if (config) {
        setConfigData({
          name: config.name,
          // 临时修改：如果原来是default样式，强制改为red样式
          styleName: TEMP_HIDE_DEFAULT_THEME && config.styleName === 'default' ? 'red' : config.styleName,
          showAvatar: config.showAvatar,
          showName: config.showName,
          showTitle: config.showTitle,
          showPosition: config.showPosition,
          showCompany: config.showCompany,
          showDepartment: config.showDepartment,
          showMobile: config.showMobile,
          showTelephone: config.showTelephone,
          showEmail: config.showEmail,
          showAddress: config.showAddress,
          public: config.public,
        });
      }
    }
  }, [isEditMode, cardConfigs, configId]);

  const handleCheckboxChange = (field: keyof Partial<CardConfig>, checked: boolean) => {
    setConfigData((prev) => ({
      ...prev,
      [field]: checked,
    }));
  };

  const handleSubmit = async () => {
    if (!configData.name?.trim()) {
      Taro.showToast({
        title: '请输入配置名称',
        icon: 'none',
      });
      return;
    }

    try {
      if (isEditMode && configId) {
        // 编辑模式：更新现有配置
        await updateCardConfig({ id: configId, config: configData });

        Taro.showToast({
          title: '更新成功',
          icon: 'success',
        });
      } else {
        // 创建模式：创建新配置
        await createCardConfig(configData);

        Taro.showToast({
          title: '创建成功',
          icon: 'success',
        });
      }

      // 无论创建还是编辑，都在成功后返回上一页
      setTimeout(() => {
        Taro.navigateBack();
      }, 1500);
    } catch (error) {
      console.error('保存配置失败', error);

      // 根据错误类型显示不同的提示
      if (error instanceof Error) {
        if (error.message.includes('名片配置名称已存在')) {
          Taro.showToast({
            title: '名片配置名称已存在',
            icon: 'none',
          });
        } else if (error.message.includes('未找到名片信息')) {
          Taro.showToast({
            title: '未找到名片信息',
            icon: 'none',
          });
        } else if (error.message.includes('未登录') || error.message.includes('401')) {
          Taro.showToast({
            title: '登录已过期，请重新登录',
            icon: 'none',
          });
          // 跳转到登录页
          setTimeout(() => {
            Taro.navigateTo({
              url: '/pages/login/index',
            });
          }, 1500);
        } else {
          Taro.showToast({
            title: isEditMode ? '更新失败' : '创建失败',
            icon: 'error',
          });
        }
      } else {
        Taro.showToast({
          title: '保存失败',
          icon: 'error',
        });
      }
    }
  };

  const ConfigRow = ({
    name,
    label,
    note,
    disabled,
  }: {
    name: keyof Partial<CardConfig>;
    label: string;
    note?: string;
    disabled?: boolean;
  }) => {
    return (
      <View className="mb-2 flex h-8 items-center">
        <Checkbox
          value={String(name)}
          checked={typeof configData[name] === 'boolean' ? (configData[name] as boolean) : false}
          onClick={() => handleCheckboxChange(name, !(configData[name] as boolean))}
          disabled={disabled}
        />
        <Text className="ml-2">{label}</Text>
        <Text className="ml-2 text-xs text-gray-500">{note}</Text>
      </View>
    );
  };

  // 样式选择组件
  const StyleSelector = () => {
    const styles = [
      { value: 'default', label: '默认样式', description: '蓝色主题的经典样式' },
      { value: 'red', label: '红色主题', description: '红色主题的现代样式' },
    ];

    return (
      <View className="mb-4 w-4/5">
        {styles.map((style) => (
          <View
            key={style.value}
            className={`mb-2 flex items-center rounded-lg border p-3 ${
              configData.styleName === style.value
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200'
            }`}
            onClick={() => setConfigData({ ...configData, styleName: style.value })}
          >
            <Radio
              value={style.value}
              checked={configData.styleName === style.value}
              className="mr-2"
            />
            <View className="flex flex-col">
              <Text className="font-bold">{style.label}</Text>
              <Text className="text-xs text-gray-500">{style.description}</Text>
            </View>
          </View>
        ))}
      </View>
    );
  };

  const renderConfigForm = () => {
    return (
      <PageView className="flex-col justify-start">
        <Text className="my-6 text-lg font-bold">
          {isEditMode ? '编辑名片配置' : '请选择要在名片上展示的信息：'}
        </Text>
        <View className="mb-4 w-4/5">
          <ConfigRow name="showAvatar" label="头像" note="不展示时使用默认头像" />
          <ConfigRow name="showName" label="姓名" note="不展示时显示'联系人'" />
          <ConfigRow name="showTitle" label="职位" />
          <ConfigRow name="showPosition" label="职务" />
          <ConfigRow name="showDepartment" label="部门" />
          <ConfigRow name="showCompany" label="公司名称" />
          <ConfigRow name="showMobile" label="手机号" />
          <ConfigRow name="showTelephone" label="座机号" />
          <ConfigRow name="showEmail" label="邮箱" />
          <ConfigRow name="showAddress" label="地址" />
          <View className="h-[1px] w-full bg-gray-200" />
          <View className="mt-4">
            <ConfigRow name="public" label="公开此名片" note="允许他人通过分享链接查看" />
          </View>
          <View className="mt-4">
            <Text className="mb-1 block text-sm">配置名称：</Text>
            <Input
              className="w-full rounded border border-gray-300 p-2"
              type="text"
              placeholder="请输入配置名称，方便日后识别"
              value={configData.name}
              onInput={(e) => setConfigData({ ...configData, name: e.detail.value })}
            />
            <Text className="text-xs text-gray-500">配置名称不可重复</Text>
          </View>
          {/* 临时隐藏样式选择器 - TODO: 这是临时修改，需要时可以轻松复原 */}
          {!TEMP_HIDE_DEFAULT_THEME && (
            <View className="mt-4">
              <Text className="mb-1 block text-sm font-bold">名片样式：</Text>
              <StyleSelector />
            </View>
          )}
        </View>

        <View className="sticky bottom-0 mt-4 w-full border-t border-gray-200 bg-white pt-2 pb-8 shadow-md">
          <View className="flex flex-row flex-nowrap justify-between px-4">
            <Button className="flex-1 rounded-lg bg-blue-500 text-white" onClick={handleSubmit}>
              {isEditMode ? '保存修改' : '保存配置'}
            </Button>
          </View>
        </View>
      </PageView>
    );
  };

  return renderConfigForm();
}
