import { Button, Text } from '@tarojs/components';
import {
  contactCardSVGSource,
  personAddSVGSource,
  phoneCallSVGSource,
  shareSVGSource
} from '../../../../assets/icon';

type ActionButtonType = 'call' | 'addContact' | 'share' | 'myCard';

type ActionButtonProps = {
  type: ActionButtonType;
  onClick: () => void;
  className?: string;
};

const actionButtonMap: Record<
  ActionButtonType,
  {
    text: string;
    icon: string;
    color: string;
    textColor: string;
  }
> = {
  call: {
    text: '立即联系',
    icon: phoneCallSVGSource,
    color: 'border-red-700 border-2 bg-white',
    textColor: 'text-red-700',
  },
  addContact: {
    text: '存入通讯录',
    icon: personAddSVGSource,
    color: 'bg-red-700 text-white',
    textColor: 'text-white',
  },
  share: {
    text: '一键分享',
    icon: shareSVGSource,
    color: 'border-red-700 border-2 bg-white',
    textColor: 'text-red-700',
  },
  myCard: {
    text: '我的名片',
    icon: contactCardSVGSource,
    color: 'bg-red-700 text-white',
    textColor: 'text-white',
  },
};

export function ActionButton({ type, onClick, className }: ActionButtonProps) {
  const { text, icon, color, textColor } = actionButtonMap[type];
  return (
    <Button
      className={`flex items-center justify-center rounded-[100rpx] ${color} w-full py-2.5 text-center ${className}`}
      onClick={onClick}
      openType={type === 'share' ? 'share' : undefined}
    >
      <Text className={`ml-2 text-base font-normal ${textColor}`}>{text}</Text>
    </Button>
  );
}
