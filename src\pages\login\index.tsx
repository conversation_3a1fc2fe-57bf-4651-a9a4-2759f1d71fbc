import { Image, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { MaterialCard, PageView } from '../../components';
import LoginWithPhoneButton from '../../components/LoginWithPhoneButton';
import { useWechatLogin } from '../../hooks';

import logo from '../../assets/logo/logo_cn_with_en_horizental.svg';
import { smartphoneSVGSource } from '../../assets/icon';
import { SVG } from '../../assets/icon';

export default function Login() {
  const { isLoggedIn } = useWechatLogin();

  // 登录成功后跳转到名片管理页面
  if (isLoggedIn) {
    setTimeout(() => {
      Taro.redirectTo({
        url: '/pages/card/manage',
      });
    }, 1000);
  }

  // 处理登录成功
  const handleLoginSuccess = (success: boolean) => {
    if (success) {
      Taro.showToast({
        title: '登录成功',
        icon: 'success',
      });
    }
  };

  // 处理登录失败
  const handleLoginFailed = (error: string) => {
    console.error('登录失败:', error);
    Taro.showToast({
      title: '登录失败',
      icon: 'error',
    });
  };

  return (
    <PageView>
      <MaterialCard className="w-3/5 max-w-md p-5">
        <View className="flex flex-col items-center justify-center">
          <Image src={logo} className="h-10 w-3/5" mode="aspectFit" />
        </View>

        {isLoggedIn ? (
          <View className="flex flex-col items-center justify-center">
            <Text>您已成功登录！</Text>
            <Text>正在跳转至名片配置页面...</Text>
          </View>
        ) : (
          <View className="mt-8 flex flex-col items-center justify-center">
            <Text className="mb-4 text-center text-gray-600">请使用微信手机号登录</Text>
            <LoginWithPhoneButton
              className="rounded-md bg-green-500 px-4 py-2 text-white"
              onLogin={handleLoginSuccess}
              onFailed={handleLoginFailed}
            >
              <View className="flex flex-row items-center gap-2">
                <SVG src={smartphoneSVGSource} size={24} />
                <Text>微信手机号登录</Text>
              </View>
            </LoginWithPhoneButton>
          </View>
        )}
      </MaterialCard>
    </PageView>
  );
}
