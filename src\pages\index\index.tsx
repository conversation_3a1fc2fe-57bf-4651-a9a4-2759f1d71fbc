import { Image, ScrollView, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import logo from '../../assets/logo/logo_cn_with_en_horizental.svg';
import { Button } from '../../components/Button';
import { BottomCard } from '../../components/Intro/bottomCard';
import { useWechatLogin } from '../../hooks';

import { smartphoneSVGSource, SVG } from '../../assets/icon';
import arrowUpDouble from '../../assets/icon/arrowUpDouble.svg';
import LoginWithPhoneButton from '../../components/LoginWithPhoneButton';

export default function Index() {
  const { logout, isLoggedIn } = useWechatLogin();

  const navigateToCardConfig = () => {
    Taro.redirectTo({
      url: '/pages/card/manage',
    });
  };

  const navigateToApiTest = () => {
    Taro.redirectTo({
      url: '/pages/api-test/index',
    });
  };

  // 处理登录成功
  const handleLoginSuccess = (success: boolean) => {
    if (success) {
      Taro.showToast({
        title: '登录成功',
        icon: 'success',
      });
      setTimeout(navigateToCardConfig, 1000);
    }
  };

  // 处理登录失败
  const handleLoginFailed = (error: string) => {
    console.error('登录失败:', error);
    Taro.showModal({
      title: '登录失败',
      content: '您的手机号尚未获得系统访问授权。\n\n本应用仅限公司内部员工使用，如需开通权限请联系管理员。',
      showCancel: false,
      confirmText: '我知道了',
    });
  };

  const handleLoginCancel = () => {
    Taro.showToast({
      title: '已取消登录',
      icon: 'none',
    });
  };

  return (
    <ScrollView className="flex w-full flex-col items-center justify-center" enableFlex>
      <View className="flex h-[85vh] flex-col items-center justify-center px-4">
        <Image src={logo} className="h-6" mode="aspectFit" />
        <Text className="text-md mt-5 font-medium text-gray-400">- 电子名片 -</Text>

        {/* 使用人群说明 */}
        <View className="flex flex-col items-center mt-6 rounded-lg bg-blue-50 px-4 py-3 border border-blue-200">
          <Text className="text-center text-sm text-blue-700 font-medium">
            使用说明
          </Text>
          <Text className="mt-2 text-center text-xs text-blue-600 leading-relaxed">
            本应用仅限公司内部员工使用{'\n'}
            需使用系统内预先注册的手机号进行登录
          </Text>
        </View>

        {isLoggedIn ? (
          <View className="flex flex-col items-center">
            <Button onClick={navigateToCardConfig} type="primary" className="mt-5">
              前往名片配置
            </Button>
            <Button onClick={logout} className="mt-2">
              登出
            </Button>
          </View>
        ) : (
          <View className="flex flex-col items-center">
            {/* 账号鉴权说明 */}
            <View className="mt-4 rounded-md bg-amber-50 px-3 py-2 border border-amber-200">
              <Text className="text-center text-xs text-amber-700">
                🔐 仅限已授权的手机号可登录系统
              </Text>
            </View>

            <LoginWithPhoneButton
              className="mt-4 rounded-md bg-green-500 px-4 text-white"
              onLogin={handleLoginSuccess}
              onFailed={handleLoginFailed}
              onCancel={handleLoginCancel}
            >
              <View className="flex flex-row items-center gap-2">
                <SVG src={smartphoneSVGSource} size={24} />
                <Text>手机号登录</Text>
              </View>
            </LoginWithPhoneButton>
          </View>
        )}
        {process.env.NODE_ENV === 'development' && (
          <Button className="mt-5" onClick={navigateToApiTest}>
            测试API
          </Button>
        )}
      </View>

      <View className="z-10 flex h-10 w-full items-center justify-center">
        <SVG src={arrowUpDouble} className="h-10 w-10" />
      </View>

      <BottomCard />
    </ScrollView>
  );
}
