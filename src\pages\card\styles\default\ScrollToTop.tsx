import Taro from '@tarojs/taro';
import { View } from '@tarojs/components';

import { SVG } from '../../../../assets/icon';
import arrowUp from '../../../../assets/icon/arrowUp.svg';

export const ScrollToTop = () => {
  const handleScrollToTop = () => {
    // 获取ScrollView元素并滚动到顶部
    const scrollView = document.getElementById('scrollview');
    if (scrollView) {
      scrollView.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    } else {
      // 如果无法直接获取DOM元素，使用Taro API
      Taro.pageScrollTo({
        scrollTop: 0,
        duration: 300,
      });
    }
  };

  return (
    <View
      className="fixed right-4 bottom-6 flex h-10 w-10 items-center justify-center bg-blue-600 shadow-lg animate-fade-in"
      style={{
        borderRadius: '100%',
        animation: 'fadeIn 0.3s ease-in-out',
        transition: 'transform 0.2s',
      }}
      hoverClass="scale-105"
      hoverStayTime={100}
      onClick={handleScrollToTop}
    >
      <SVG src={arrowUp} size={24} />
    </View>
  );
};

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
`;
document.head.appendChild(style);
