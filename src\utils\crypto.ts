/**
 * 加密工具函数
 */
import md5 from 'md5';
import { sm4 } from 'miniprogram-sm-crypto';
import WxmpRsa from 'wxmp-rsa';
import { SM4_KEY } from '../api/api.config';

const rsa = new WxmpRsa();

/**
 * 使用SM4加密
 * @param text 要加密的文本
 * @param key SM4密钥
 * @returns 加密后的文本
 */
export function encryptSM4(text: string, key: string = SM4_KEY): string {
  const hexKey = stringToHexString(key);
  const encrypted = sm4.encrypt(text, hexKey);
  return hexToBase64(encrypted);
}

/**
 * 解密SM4加密的文本
 * @param ciphertext 加密文本
 * @param key SM4密钥
 * @returns 解密后的文本
 */
export function decryptSM4(ciphertext: string, key: string = SM4_KEY): string {
  const hexKey = stringToHexString(key);
  const hexText = base64ToHex(ciphertext);
  return sm4.decrypt(hexText, hexKey);
}

/**
 * 使用RSA加密
 * @param text 要加密的文本
 * @param publicKey RSA公钥
 * @returns 加密后的文本
 */
export function encryptRSA(text: string, publicKey: string): string {
  rsa.setPublicKey(publicKey);
  return rsa.encryptLong(text);
}

/**
 * 对文本进行MD5加密并转为大写
 * @param text 要加密的文本
 * @returns MD5加密后的大写文本
 */
export function md5Upper(text: string): string {
  return md5(text).toUpperCase();
}

function stringToHexString(str: string) {
  let hexStr = '';
  for (let i = 0; i < str.length; i++) {
    const hex = str.charCodeAt(i).toString(16).padStart(2, '0');
    hexStr += hex;
  }
  return hexStr;
}

// Base64 字符表
const BASE64_CHARS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';

function hexToBase64(hexStr: string) {
  // 1. 16 进制字符串 → 字节数组（Uint8Array）
  const bytes = new Uint8Array(hexStr.length / 2);
  for (let i = 0; i < hexStr.length; i += 2) {
    bytes[i / 2] = Number.parseInt(hexStr.substr(i, 2), 16);
  }

  // 2. 字节数组 → Base64
  let base64 = '';
  for (let i = 0; i < bytes.length; i += 3) {
    // 每次处理 3 个字节（24 bits）
    const byte1 = bytes[i];
    const byte2 = bytes[i + 1] || 0;
    const byte3 = bytes[i + 2] || 0;

    // 将 3 字节拆分为 4 个 6-bit 数字
    const chunk = (byte1 << 16) | (byte2 << 8) | byte3;
    const b1 = (chunk >> 18) & 0x3f;
    const b2 = (chunk >> 12) & 0x3f;
    const b3 = (chunk >> 6) & 0x3f;
    const b4 = chunk & 0x3f;

    // 映射到 Base64 字符
    base64 += BASE64_CHARS[b1] + BASE64_CHARS[b2] + BASE64_CHARS[b3] + BASE64_CHARS[b4];
  }

  // 处理填充（Padding）
  const padding = bytes.length % 3;
  if (padding === 1) {
    base64 = `${base64.slice(0, -2)}==`;
  } else if (padding === 2) {
    base64 = `${base64.slice(0, -1)}=`;
  }

  return base64;
}

// Base64 字符串转 16 进制字符串
function base64ToHex(base64Str: string): string {
  // 移除填充字符
  const base64StrWithoutPadding = base64Str.replace(/=+$/, '');

  // 将 Base64 字符转换为对应的 6-bit 值
  let binaryStr = '';
  for (let i = 0; i < base64StrWithoutPadding.length; i++) {
    const char = base64StrWithoutPadding[i];
    const index = BASE64_CHARS.indexOf(char);
    if (index === -1) continue;
    binaryStr += index.toString(2).padStart(6, '0');
  }

  // 将二进制字符串转换为字节数组
  const bytes: number[] = [];
  for (let i = 0; i < binaryStr.length; i += 8) {
    const byteStr = binaryStr.substr(i, 8);
    if (byteStr.length < 8) continue;
    bytes.push(Number.parseInt(byteStr, 2));
  }

  // 将字节数组转换为 16 进制字符串
  let hexStr = '';
  for (let i = 0; i < bytes.length; i++) {
    hexStr += bytes[i].toString(16).padStart(2, '0');
  }

  return hexStr;
}
